<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class UserManagementController extends Controller
{
    /**
     * Display a listing of users.
     */
    public function index(Request $request)
    {
        $query = User::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'ILIKE', "%{$search}%")
                    ->orWhere('email', 'ILIKE', "%{$search}%")
                    ->orWhere('phone', 'ILIKE', "%{$search}%");
            });
        }

        // Filter by role
        if ($request->filled('role') && $request->role !== 'all') {
            $query->where('role', $request->role);
        }

        // Filter by status
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        // Filter by verification status
        if ($request->filled('verified') && $request->verified !== 'all') {
            if ($request->verified === 'verified') {
                $query->where('is_verified', true);
            } else {
                $query->where('is_verified', false);
            }
        }

        $users = $query->withCount(['projects', 'bids'])
            ->latest()
            ->paginate(20)
            ->withQueryString();

        return Inertia::render('Admin/UserManagement', [
            'users' => $users,
            'filters' => $request->only(['search', 'role', 'status', 'verified']),
            'stats' => [
                'total_users' => User::count(),
                'active_users' => User::where('status', 'active')->count(),
                'suspended_users' => User::where('status', 'suspended')->count(),
                'blocked_users' => User::where('status', 'blocked')->count(),
                'verified_users' => User::where('is_verified', true)->count(),
            ],
        ]);
    }

    /**
     * Show the form for editing the specified user.
     */
    public function show(User $user)
    {
        $user->load(['projects', 'bids', 'walletTransactions' => function ($query) {
            $query->latest()->limit(10);
        }]);

        return Inertia::render('Admin/UserDetails', [
            'user' => $user,
            'user_stats' => [
                'total_projects' => $user->projects()->count(),
                'total_bids' => $user->bids()->count(),
                'total_spent' => $user->walletTransactions()->where('type', 'payment')->sum('amount'),
                'total_earned' => $user->walletTransactions()->where('type', 'deposit')->sum('amount'),
                'average_rating' => $user->averageRating(),
                'total_ratings' => $user->totalRatings(),
            ],
        ]);
    }

    /**
     * Update the specified user.
     */
    public function update(Request $request, User $user)
    {
        // Prevent self-modification of super admin
        if ($user->isSuperAdmin() && $user->id === Auth::id()) {
            return back()->withErrors(['error' => 'Cannot modify your own super admin account.']);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'role' => 'required|in:user,admin,super_admin',
            'status' => 'required|in:active,suspended,blocked',
            'is_verified' => 'boolean',
            'bio' => 'nullable|string|max:1000',
            'phone' => 'nullable|string|max:20',
            'location' => 'nullable|string|max:100',
            'skills' => 'nullable|array',
            'education' => 'nullable|string|max:255',
        ]);

        $user->update($request->all());

        return back()->with('success', 'User updated successfully.');
    }

    /**
     * Update user status.
     */
    public function updateStatus(Request $request, User $user)
    {
        // Prevent self-modification of super admin
        if ($user->isSuperAdmin() && $user->id === Auth::id()) {
            return back()->withErrors(['error' => 'Cannot modify your own status.']);
        }

        $request->validate([
            'status' => 'required|in:active,suspended,blocked',
            'reason' => 'nullable|string|max:500',
        ]);

        $user->update(['status' => $request->status]);

        $action = $request->status === 'active' ? 'activated' : $request->status;

        return back()->with('success', "User {$action} successfully.");
    }

    /**
     * Update user verification status.
     */
    public function updateVerification(Request $request, User $user)
    {
        $request->validate([
            'is_verified' => 'required|boolean',
        ]);

        $user->update(['is_verified' => $request->is_verified]);

        $status = $request->is_verified ? 'verified' : 'unverified';

        return back()->with('success', "User {$status} successfully.");
    }

    /**
     * Delete the specified user.
     */
    public function destroy(User $user)
    {
        // Prevent deletion of super admin
        if ($user->isSuperAdmin()) {
            return back()->withErrors(['error' => 'Cannot delete super admin account.']);
        }

        // Prevent self-deletion
        if ($user->id === Auth::id()) {
            return back()->withErrors(['error' => 'Cannot delete your own account.']);
        }

        $user->delete();

        return back()->with('success', 'User deleted successfully.');
    }

    /**
     * Adjust user wallet balance.
     */
    public function adjustWallet(Request $request, User $user)
    {
        $request->validate([
            'amount' => 'required|numeric',
            'type' => 'required|in:add,subtract',
            'reason' => 'required|string|max:255',
        ]);

        $amount = $request->amount;
        $oldBalance = $user->wallet_balance;

        if ($request->type === 'subtract') {
            if ($oldBalance < $amount) {
                return back()->withErrors(['error' => 'Insufficient balance for this operation.']);
            }
            $newBalance = $oldBalance - $amount;
            $transactionType = 'withdrawal';
        } else {
            $newBalance = $oldBalance + $amount;
            $transactionType = 'deposit';
        }

        // Update user balance
        $user->update(['wallet_balance' => $newBalance]);

        // Create transaction record
        $user->walletTransactions()->create([
            'transaction_id' => 'admin_'.time().'_'.$user->id,
            'type' => $transactionType,
            'amount' => $amount,
            'balance_before' => $oldBalance,
            'balance_after' => $newBalance,
            'status' => 'completed',
            'payment_method' => 'admin_adjustment',
            'description' => 'Admin adjustment: '.$request->reason,
            'metadata' => [
                'admin_id' => Auth::id(),
                'admin_name' => Auth::user()->name,
                'reason' => $request->reason,
            ],
        ]);

        return back()->with('success', 'Wallet balance adjusted successfully.');
    }
}
