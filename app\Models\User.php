<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'google_id',
        'role',
        'status',
        'wallet_balance',
        'bio',
        'skills',
        'education',
        'is_verified',
        'phone',
        'location',
        'last_activity',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'skills' => 'array',
            'last_activity' => 'datetime',
            'wallet_balance' => 'decimal:2',
        ];
    }

    /**
     * Check if user is admin.
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Check if user is super admin.
     */
    public function isSuperAdmin(): bool
    {
        return $this->role === 'super_admin';
    }

    /**
     * Check if user can access admin features.
     */
    public function canAccessAdmin(): bool
    {
        return in_array($this->role, ['admin', 'super_admin']);
    }

    /**
     * Projects posted by this user (as client).
     */
    public function projects()
    {
        return $this->hasMany(Project::class);
    }

    /**
     * Bids made by this user (as freelancer).
     */
    public function bids()
    {
        return $this->hasMany(Bid::class);
    }

    /**
     * Wallet transactions for this user.
     */
    public function walletTransactions()
    {
        return $this->hasMany(WalletTransaction::class);
    }

    /**
     * Ratings given by this user.
     */
    public function givenRatings()
    {
        return $this->hasMany(Rating::class, 'client_id')->orWhere('freelancer_id', $this->id);
    }

    /**
     * Ratings received by this user.
     */
    public function receivedRatings()
    {
        return $this->hasMany(Rating::class, 'freelancer_id')->orWhere('client_id', $this->id);
    }

    /**
     * Calculate average rating for this user.
     */
    public function averageRating(): float
    {
        return $this->receivedRatings()->avg('rating') ?: 0;
    }

    /**
     * Get total rating count.
     */
    public function totalRatings(): int
    {
        return $this->receivedRatings()->count();
    }
}
