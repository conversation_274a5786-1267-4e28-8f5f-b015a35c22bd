<?php

namespace Database\Seeders;

use App\Models\Bid;
use App\Models\Project;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class ProjectSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $clients = User::whereIn('email', [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ])->get();

        $freelancers = User::whereIn('email', [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ])->get();

        $sampleProjects = [
            [
                'title' => 'E-Commerce Website Development',
                'description' => 'Need to develop a complete e-commerce website for my final year project. The website should include user authentication, product catalog, shopping cart, payment integration, and admin panel.',
                'requirements' => 'Technologies required: Laravel/PHP, MySQL database, responsive design, payment gateway integration (PayStack), admin dashboard for inventory management.',
                'budget_min' => 800,
                'budget_max' => 1500,
                'budget_type' => 'fixed',
                'deadline' => now()->addMonths(2),
                'category' => 'Web Development',
                'academic_level' => 'undergraduate',
                'status' => 'open',
            ],
            [
                'title' => 'Machine Learning Model for Stock Price Prediction',
                'description' => 'Looking for assistance in developing a machine learning model to predict stock prices using historical data. This is for my computer science final year project.',
                'requirements' => 'Python, TensorFlow/PyTorch, data preprocessing, model training, evaluation metrics, documentation, and presentation slides.',
                'budget_min' => 600,
                'budget_max' => 1200,
                'budget_type' => 'fixed',
                'deadline' => now()->addMonths(3),
                'category' => 'Data Science',
                'academic_level' => 'undergraduate',
                'status' => 'open',
            ],
            [
                'title' => 'Mobile App for Campus Event Management',
                'description' => 'Need to create a mobile application for managing campus events. Students should be able to view events, register, receive notifications, and provide feedback.',
                'requirements' => 'React Native or Flutter, Firebase backend, push notifications, user authentication, admin panel for event management.',
                'budget_min' => 1000,
                'budget_max' => 2000,
                'budget_type' => 'fixed',
                'deadline' => now()->addMonths(4),
                'category' => 'Mobile Development',
                'academic_level' => 'undergraduate',
                'status' => 'open',
            ],
            [
                'title' => 'Business Plan for Sustainable Agriculture Startup',
                'description' => 'Seeking help to develop a comprehensive business plan for a sustainable agriculture startup in Ghana. This includes market research, financial projections, and sustainability analysis.',
                'requirements' => 'Market research, competitor analysis, financial modeling, sustainability framework, executive summary, PowerPoint presentation.',
                'budget_min' => 400,
                'budget_max' => 800,
                'budget_type' => 'fixed',
                'deadline' => now()->addMonths(2),
                'category' => 'Business',
                'academic_level' => 'undergraduate',
                'status' => 'open',
            ],
            [
                'title' => 'IoT-Based Smart Home Automation System',
                'description' => 'Final year engineering project to develop an IoT-based smart home automation system. Need help with hardware integration, software development, and documentation.',
                'requirements' => 'Arduino/Raspberry Pi, sensors, mobile app development, cloud integration, circuit design, technical documentation.',
                'budget_min' => 700,
                'budget_max' => 1300,
                'budget_type' => 'fixed',
                'deadline' => now()->addMonths(3),
                'category' => 'Engineering',
                'academic_level' => 'undergraduate',
                'status' => 'in_progress',
            ],
            [
                'title' => 'Financial Analysis of Ghanaian Banking Sector',
                'description' => 'Need assistance with comprehensive financial analysis of the Ghanaian banking sector for my finance final year project. Includes ratio analysis, performance metrics, and recommendations.',
                'requirements' => 'Financial statement analysis, ratio calculations, trend analysis, industry comparison, Excel modeling, written report.',
                'budget_min' => 500,
                'budget_max' => 900,
                'budget_type' => 'fixed',
                'deadline' => now()->addMonths(2),
                'category' => 'Finance',
                'academic_level' => 'undergraduate',
                'status' => 'completed',
            ],
            [
                'title' => 'AI Chatbot for Customer Service',
                'description' => 'Developing an AI-powered chatbot for customer service automation. Need help with natural language processing, training data preparation, and integration.',
                'requirements' => 'Python, NLP libraries (spaCy, NLTK), machine learning frameworks, API development, frontend integration.',
                'budget_min' => 900,
                'budget_max' => 1600,
                'budget_type' => 'fixed',
                'deadline' => now()->addMonths(3),
                'category' => 'Artificial Intelligence',
                'academic_level' => 'graduate',
                'status' => 'open',
            ],
            [
                'title' => 'Blockchain-Based Voting System',
                'description' => 'Final year project to create a secure blockchain-based voting system. Need expertise in blockchain development, smart contracts, and security implementation.',
                'requirements' => 'Solidity, Ethereum/Polygon, Web3.js, security auditing, user interface design, deployment and testing.',
                'budget_min' => 1200,
                'budget_max' => 2500,
                'budget_type' => 'fixed',
                'deadline' => now()->addMonths(4),
                'category' => 'Blockchain',
                'academic_level' => 'graduate',
                'status' => 'open',
            ],
        ];

        foreach ($sampleProjects as $index => $projectData) {
            $client = $clients->random();

            $project = Project::create(array_merge($projectData, [
                'user_id' => $client->id,
                'slug' => Str::slug($projectData['title']).'-'.Str::random(6),
            ]));

            // Add some bids to projects
            if (in_array($project->status, ['open', 'in_progress', 'completed'])) {
                $bidCount = $project->status === 'open' ? rand(2, 5) : rand(3, 7);

                for ($i = 0; $i < $bidCount; $i++) {
                    $freelancer = $freelancers->random();

                    // Avoid duplicate bids from same freelancer
                    $existingBid = Bid::where('project_id', $project->id)
                        ->where('user_id', $freelancer->id)
                        ->exists();

                    if (! $existingBid) {
                        $bidAmount = rand((int) $project->budget_min, (int) $project->budget_max);
                        $deliveryDays = rand(7, 30);

                        $proposals = [
                            'I have extensive experience in this field and can deliver high-quality work within the specified timeframe.',
                            'Having worked on similar projects, I understand your requirements and can provide excellent results.',
                            "I'm confident in my ability to exceed your expectations. Let's discuss the project details further.",
                            'With my background in this domain, I can bring innovative solutions to your project.',
                            'I have the skills and experience needed to successfully complete this project to your satisfaction.',
                        ];

                        $status = 'pending';
                        if ($project->status === 'in_progress' && $i === 0) {
                            $status = 'accepted';
                        } elseif ($project->status === 'completed' && $i === 0) {
                            $status = 'accepted';
                        }

                        Bid::create([
                            'project_id' => $project->id,
                            'user_id' => $freelancer->id,
                            'amount' => $bidAmount,
                            'proposal' => $proposals[array_rand($proposals)],
                            'delivery_days' => $deliveryDays,
                            'status' => $status,
                        ]);
                    }
                }
            }
        }

        // Create additional random projects
        $remainingClients = User::where('role', 'user')
            ->whereNotIn('id', $clients->pluck('id'))
            ->limit(10)
            ->get();

        foreach ($remainingClients as $client) {
            Project::factory()->create([
                'user_id' => $client->id,
                'status' => fake()->randomElement(['open', 'open', 'open', 'in_progress', 'completed']),
            ]);
        }
    }
}
