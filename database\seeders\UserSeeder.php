<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Super Admin (You)
        User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'super_admin',
            'status' => 'active',
            'email_verified_at' => now(),
            'wallet_balance' => 1000.00,
            'bio' => 'Super Administrator and Developer of TheSylink platform.',
            'skills' => ['Laravel', 'React', 'Project Management', 'System Administration'],
            'education' => 'Computer Science',
            'is_verified' => true,
            'phone' => '+1234567890',
            'location' => 'Ghana',
            'last_activity' => now(),
        ]);

        // Create Regular Admin
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'admin',
            'status' => 'active',
            'email_verified_at' => now(),
            'wallet_balance' => 500.00,
            'bio' => 'Platform Administrator helping manage the TheSylink community.',
            'skills' => ['Project Management', 'Customer Support', 'Content Moderation'],
            'education' => 'Business Administration',
            'is_verified' => true,
            'phone' => '+1234567891',
            'location' => 'Ghana',
            'last_activity' => now(),
        ]);

        // Create Test Client Users
        $clients = [
            [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'bio' => 'Final year student looking for assistance with my computer science project.',
                'skills' => ['Research', 'Documentation', 'Project Planning'],
                'education' => 'BSc Computer Science',
                'location' => 'Accra, Ghana',
            ],
            [
                'name' => 'Sarah Johnson',
                'email' => '<EMAIL>',
                'bio' => 'Engineering student needing help with my capstone project.',
                'skills' => ['Engineering Design', 'Technical Writing', 'Research'],
                'education' => 'BSc Mechanical Engineering',
                'location' => 'Kumasi, Ghana',
            ],
            [
                'name' => 'Michael Chen',
                'email' => '<EMAIL>',
                'bio' => 'Business student seeking professional assistance for my final year project.',
                'skills' => ['Business Analysis', 'Market Research', 'Data Analysis'],
                'education' => 'BSc Business Administration',
                'location' => 'Tema, Ghana',
            ],
        ];

        foreach ($clients as $client) {
            User::create(array_merge($client, [
                'password' => Hash::make('password123'),
                'role' => 'user',
                'status' => 'active',
                'email_verified_at' => now(),
                'wallet_balance' => rand(100, 1000),
                'is_verified' => true,
                'phone' => '+233'.rand(100000000, 999999999),
                'last_activity' => now()->subHours(rand(1, 48)),
            ]));
        }

        // Create Test Freelancer Users
        $freelancers = [
            [
                'name' => 'Dr. Emmanuel Kwame',
                'email' => '<EMAIL>',
                'bio' => 'PhD holder with 10+ years experience in academic research and project supervision.',
                'skills' => ['Academic Writing', 'Research Methodology', 'Data Analysis', 'SPSS', 'Python', 'R'],
                'education' => 'PhD Computer Science',
                'location' => 'Legon, Ghana',
            ],
            [
                'name' => 'Prof. Akosua Mensah',
                'email' => '<EMAIL>',
                'bio' => 'University lecturer specializing in engineering projects and technical documentation.',
                'skills' => ['Engineering Design', 'AutoCAD', 'MATLAB', 'Technical Writing', 'Project Management'],
                'education' => 'PhD Mechanical Engineering',
                'location' => 'Kumasi, Ghana',
            ],
            [
                'name' => 'James Asante',
                'email' => '<EMAIL>',
                'bio' => 'Software developer and consultant with expertise in web development and mobile apps.',
                'skills' => ['Laravel', 'React', 'Node.js', 'Python', 'Mobile Development', 'Database Design'],
                'education' => 'MSc Software Engineering',
                'location' => 'Accra, Ghana',
            ],
            [
                'name' => 'Grace Osei',
                'email' => '<EMAIL>',
                'bio' => 'Business consultant and researcher with focus on market analysis and business strategy.',
                'skills' => ['Business Strategy', 'Market Research', 'Financial Analysis', 'Excel', 'PowerBI'],
                'education' => 'MBA Business Strategy',
                'location' => 'Takoradi, Ghana',
            ],
            [
                'name' => 'Daniel Appiah',
                'email' => '<EMAIL>',
                'bio' => 'Data scientist and statistician helping students with data-driven projects.',
                'skills' => ['Data Science', 'Machine Learning', 'Python', 'R', 'SQL', 'Statistics'],
                'education' => 'MSc Data Science',
                'location' => 'Accra, Ghana',
            ],
        ];

        foreach ($freelancers as $freelancer) {
            User::create(array_merge($freelancer, [
                'password' => Hash::make('password123'),
                'role' => 'user',
                'status' => 'active',
                'email_verified_at' => now(),
                'wallet_balance' => rand(200, 2000),
                'is_verified' => true,
                'phone' => '+233'.rand(100000000, 999999999),
                'last_activity' => now()->subHours(rand(1, 24)),
            ]));
        }

        // Create some regular users (mix of potential clients and freelancers)
        User::factory(15)->create([
            'role' => 'user',
            'status' => 'active',
            'email_verified_at' => now(),
            'is_verified' => false,
            'wallet_balance' => 0,
        ]);
    }
}
