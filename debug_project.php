<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\EscrowTransaction;
use App\Models\Project;
use App\Models\ProjectMilestone;

// Find the project
$project = Project::where('slug', 'e-commerce-website-development-YEOOQW')->first();

if (! $project) {
    echo "Project not found!\n";
    exit;
}

echo 'Project ID: '.$project->id."\n";
echo 'Project Title: '.$project->title."\n";
echo 'Project Status: '.$project->status."\n";
echo 'Assigned Freelancer ID: '.$project->assigned_freelancer_id."\n";
echo 'Accepted Bid Amount: '.$project->accepted_bid_amount."\n";
echo 'Escrow Amount: '.$project->escrow_amount."\n";
echo 'Escrow Status: '.$project->escrow_status."\n";
echo 'Total Milestones: '.$project->total_milestones."\n";
echo 'Completed Milestones: '.$project->completed_milestones."\n";

// Check milestones
$milestones = ProjectMilestone::where('project_id', $project->id)->get();
echo "\nMilestones Count: ".$milestones->count()."\n";

foreach ($milestones as $milestone) {
    echo "- Milestone {$milestone->order_index}: {$milestone->title} (Status: {$milestone->status})\n";
}

// Check escrow transactions
$escrowTransactions = EscrowTransaction::where('project_id', $project->id)->get();
echo "\nEscrow Transactions Count: ".$escrowTransactions->count()."\n";

foreach ($escrowTransactions as $transaction) {
    echo "- Transaction: {$transaction->type} - Amount: {$transaction->amount} - Freelancer Amount: {$transaction->freelancer_amount} - Commission: {$transaction->commission_amount} (Status: {$transaction->status})\n";
}

// Check platform commissions
use App\Models\PlatformCommission;

$commissions = PlatformCommission::where('project_id', $project->id)->get();
echo "\nPlatform Commissions Count: ".$commissions->count()."\n";

foreach ($commissions as $commission) {
    echo "- Commission: {$commission->amount} - Rate: {$commission->commission_rate}% (Status: {$commission->status})\n";
}

// Check freelancer wallet balance
$freelancer = \App\Models\User::find($project->assigned_freelancer_id);
echo "\nFreelancer Wallet Balance: ₵".$freelancer->wallet_balance."\n";
