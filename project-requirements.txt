Final Year Project Marketplace - Development Roadmap 

This roadmap outlines the phased development approach for building the 
Final Year Project Marketplace platform using Laravel + Inertia + React. 
The platform adopts a flexible role system where all participants are 'Users'. 
Each User can act as a Client (posting projects) or as a Freelancer/Researcher (executing projects). 
Admin remains a separate system role, ans Super Admin(Myself the developer of the system). 
The roadmap prioritizes essential features first (MVP), followed by add-ons and scaling 
improvements. 

Phase 1: MVP 

- User authentication (User + Admin roles) 

- Profile creation and management (skills, bio, education, verification) 

- Ability for User to switch between Client mode and Freelancer mode 

- Project posting by Users in Client mode (title, description, files, budget, deadline...) 

- Project browsing and bidding by Users in Freelancer mode 

- Escrow-based payment system with 30% commission 

- Project status tracking (Open, In Progress, Completed) 

- Basic admin dashboard (manage users, projects, transactions) 

- Basic messaging between Clients and Freelancers 

- Security implementation (SSL, RBAC, password hashing) 

Phase 2: Add-On Features & Monetization 

- Plagiarism checker integration (API-based) 

- AI content checker integration 

- Video call feature for project defense prep (WebRTC/Twilio/Daily.co) 

- Premium project highlighting (paid boosts for visibility) 

- Freelancer subscription plans (basic vs premium access with higher bid limits) 

- Enhanced admin dashboard with revenue analytics 

- Notification system (in-app + email) 

Phase 3: User Experience & Growth 

- Ratings and reviews system (Clients rate Freelancers and vice versa) 

- Advanced messaging (file sharing, search, attachments) 

- Enhanced project milestone payments 

- Freelancer portfolio and showcase profiles 

- Improved search & filtering for projects and freelancers 

- Comprehensive reporting tools for admins