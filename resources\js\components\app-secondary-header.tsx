import { Icon } from '@/components/icon';
import { NavigationMenu, NavigationMenuItem, NavigationMenuList, navigationMenuTriggerStyle } from '@/components/ui/navigation-menu';
import { cn } from '@/lib/utils';
import { type NavItem, type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { FolderOpen, LayoutGrid, Mail } from 'lucide-react';

const secondaryNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'My Projects',
        href: '/my-projects',
        icon: FolderOpen,
    },
    {
        title: 'Browse Projects',
        href: '/browse',
        icon: FolderOpen,
    },
    {
        title: 'Inbox',
        href: '/inbox',
        icon: Mail,
    },
];

const activeItemStyles = 'text-neutral-900 dark:bg-neutral-800 dark:text-neutral-100';

export function AppSecondaryHeader() {
    const page = usePage<SharedData>();

    return (
        <div className="border-b border-sidebar-border/80 bg-background">
            <div className="mx-auto flex h-12 items-center px-4 md:max-w-7xl">
                <NavigationMenu className="flex h-full items-stretch">
                    <NavigationMenuList className="flex h-full items-stretch space-x-2">
                        {secondaryNavItems.map((item, index) => (
                            <NavigationMenuItem key={index} className="relative flex h-full items-center">
                                <Link
                                    href={item.href}
                                    className={cn(
                                        navigationMenuTriggerStyle(),
                                        page.url === item.href && activeItemStyles,
                                        'h-8 cursor-pointer px-3 text-sm',
                                    )}
                                >
                                    {item.icon && <Icon iconNode={item.icon} className="mr-2 h-3.5 w-3.5" />}
                                    {item.title}
                                </Link>
                                {page.url === item.href && (
                                    <div className="absolute bottom-0 left-0 h-0.5 w-full translate-y-px bg-black dark:bg-white"></div>
                                )}
                            </NavigationMenuItem>
                        ))}
                    </NavigationMenuList>
                </NavigationMenu>
            </div>
        </div>
    );
}
