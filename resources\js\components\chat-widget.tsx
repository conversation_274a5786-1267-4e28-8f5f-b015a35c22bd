import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { router } from '@inertiajs/react';
import { ChevronDown, ChevronUp, MessageCircle, Send, X } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

interface Message {
    id: number;
    message: string;
    sender: {
        id: number;
        name: string;
    };
    created_at: string;
    is_own: boolean;
}

interface Conversation {
    id: number;
    project: {
        id: number;
        title: string;
    };
    other_user: {
        id: number;
        name: string;
    };
    last_message?: string;
    last_message_at?: string;
    unread_count: number;
}

interface ConversationDetail {
    id: number;
    project: {
        id: number;
        title: string;
    };
    other_user: {
        id: number;
        name: string;
    };
}

export default function ChatWidget() {
    const [isOpen, setIsOpen] = useState(false);
    const [conversations, setConversations] = useState<Conversation[]>([]);
    const [activeConversation, setActiveConversation] = useState<ConversationDetail | null>(null);
    const [messages, setMessages] = useState<Message[]>([]);
    const [newMessage, setNewMessage] = useState('');
    const [loading, setLoading] = useState(false);
    const messagesEndRef = useRef<HTMLDivElement>(null);

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    const fetchConversations = async () => {
        try {
            const response = await fetch('/api/conversations');
            const data = await response.json();
            setConversations(data.conversations);
        } catch (error) {
            console.error('Failed to fetch conversations:', error);
        }
    };

    const fetchMessages = async (conversationId: number) => {
        try {
            setLoading(true);
            const response = await fetch(`/api/conversations/${conversationId}`);
            const data = await response.json();
            setActiveConversation(data.conversation);
            setMessages(data.messages);
        } catch (error) {
            console.error('Failed to fetch messages:', error);
        } finally {
            setLoading(false);
        }
    };

    const sendMessage = async () => {
        if (!newMessage.trim() || !activeConversation) return;

        try {
            const response = await fetch(`/api/conversations/${activeConversation.id}/messages`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({ message: newMessage }),
            });

            if (response.ok) {
                const data = await response.json();
                setMessages(prev => [...prev, data.message]);
                setNewMessage('');
                fetchConversations(); // Refresh conversation list
            }
        } catch (error) {
            console.error('Failed to send message:', error);
        }
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    };

    const openConversation = (conversation: Conversation) => {
        fetchMessages(conversation.id);
    };

    const closeConversation = () => {
        setActiveConversation(null);
        setMessages([]);
    };

    const toggleWidget = () => {
        if (!isOpen) {
            fetchConversations();
        }
        setIsOpen(!isOpen);
    };

    const totalUnreadCount = conversations.reduce((sum, conv) => sum + conv.unread_count, 0);

    return (
        <div className="fixed bottom-4 right-4 z-50">
            {/* Chat Widget */}
            {isOpen && (
                <Card className="mb-2 w-80 h-96 shadow-lg">
                    {activeConversation ? (
                        // Active Conversation View
                        <>
                            <CardHeader className="pb-2">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-2">
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={closeConversation}
                                            className="p-1 h-6 w-6"
                                        >
                                            <ChevronDown className="h-4 w-4" />
                                        </Button>
                                        <div>
                                            <h3 className="font-semibold text-sm">{activeConversation.other_user.name}</h3>
                                            <p className="text-xs text-muted-foreground truncate">
                                                {activeConversation.project.title}
                                            </p>
                                        </div>
                                    </div>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => setIsOpen(false)}
                                        className="p-1 h-6 w-6"
                                    >
                                        <X className="h-4 w-4" />
                                    </Button>
                                </div>
                            </CardHeader>
                            <Separator />
                            <CardContent className="p-0 flex flex-col h-full">
                                <ScrollArea className="flex-1 p-3">
                                    {loading ? (
                                        <div className="flex justify-center items-center h-full">
                                            <div className="text-sm text-muted-foreground">Loading messages...</div>
                                        </div>
                                    ) : (
                                        <div className="space-y-2">
                                            {messages.map((message) => (
                                                <div
                                                    key={message.id}
                                                    className={`flex ${message.is_own ? 'justify-end' : 'justify-start'}`}
                                                >
                                                    <div
                                                        className={`max-w-[70%] rounded-lg px-3 py-2 text-sm ${
                                                            message.is_own
                                                                ? 'bg-primary text-primary-foreground'
                                                                : 'bg-muted'
                                                        }`}
                                                    >
                                                        <p>{message.message}</p>
                                                        <p className="text-xs opacity-70 mt-1">
                                                            {new Date(message.created_at).toLocaleTimeString([], {
                                                                hour: '2-digit',
                                                                minute: '2-digit',
                                                            })}
                                                        </p>
                                                    </div>
                                                </div>
                                            ))}
                                            <div ref={messagesEndRef} />
                                        </div>
                                    )}
                                </ScrollArea>
                                <div className="p-3 border-t">
                                    <div className="flex space-x-2">
                                        <Input
                                            value={newMessage}
                                            onChange={(e) => setNewMessage(e.target.value)}
                                            onKeyPress={handleKeyPress}
                                            placeholder="Type a message..."
                                            className="flex-1"
                                        />
                                        <Button onClick={sendMessage} size="sm" disabled={!newMessage.trim()}>
                                            <Send className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </>
                    ) : (
                        // Conversations List View
                        <>
                            <CardHeader className="pb-2">
                                <div className="flex items-center justify-between">
                                    <h3 className="font-semibold">Messages</h3>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => setIsOpen(false)}
                                        className="p-1 h-6 w-6"
                                    >
                                        <X className="h-4 w-4" />
                                    </Button>
                                </div>
                            </CardHeader>
                            <Separator />
                            <CardContent className="p-0">
                                <ScrollArea className="h-80">
                                    {conversations.length === 0 ? (
                                        <div className="flex flex-col items-center justify-center h-full p-4 text-center">
                                            <MessageCircle className="h-8 w-8 text-muted-foreground mb-2" />
                                            <p className="text-sm text-muted-foreground">No conversations yet</p>
                                        </div>
                                    ) : (
                                        <div className="divide-y">
                                            {conversations.map((conversation) => (
                                                <div
                                                    key={conversation.id}
                                                    onClick={() => openConversation(conversation)}
                                                    className="p-3 hover:bg-muted cursor-pointer"
                                                >
                                                    <div className="flex items-start justify-between">
                                                        <div className="flex-1 min-w-0">
                                                            <div className="flex items-center space-x-2">
                                                                <p className="font-medium text-sm truncate">
                                                                    {conversation.other_user.name}
                                                                </p>
                                                                {conversation.unread_count > 0 && (
                                                                    <span className="bg-primary text-primary-foreground text-xs rounded-full px-2 py-0.5">
                                                                        {conversation.unread_count}
                                                                    </span>
                                                                )}
                                                            </div>
                                                            <p className="text-xs text-muted-foreground truncate">
                                                                {conversation.project.title}
                                                            </p>
                                                            {conversation.last_message && (
                                                                <p className="text-xs text-muted-foreground truncate mt-1">
                                                                    {conversation.last_message}
                                                                </p>
                                                            )}
                                                        </div>
                                                        {conversation.last_message_at && (
                                                            <span className="text-xs text-muted-foreground">
                                                                {new Date(conversation.last_message_at).toLocaleDateString()}
                                                            </span>
                                                        )}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </ScrollArea>
                            </CardContent>
                        </>
                    )}
                </Card>
            )}

            {/* Toggle Button */}
            <Button
                onClick={toggleWidget}
                className="rounded-full h-12 w-12 shadow-lg relative"
                size="sm"
            >
                {isOpen ? <ChevronDown className="h-5 w-5" /> : <ChevronUp className="h-5 w-5" />}
                {!isOpen && totalUnreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                        {totalUnreadCount > 9 ? '9+' : totalUnreadCount}
                    </span>
                )}
            </Button>
        </div>
    );
}
