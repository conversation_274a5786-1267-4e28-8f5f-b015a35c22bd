import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/app-layout';
import { Head, router, useForm } from '@inertiajs/react';
import { 
    BarChart3, 
    TrendingUp, 
    Users, 
    FolderOpen, 
    DollarSign, 
    Target, 
    Activity,
    Calendar,
    Filter,
    Download
} from 'lucide-react';

interface UserStats {
    total_users: number;
    new_users_this_month: number;
    active_users: number;
    verified_users: number;
    users_by_role: Record<string, number>;
}

interface ProjectStats {
    total_projects: number;
    open_projects: number;
    in_progress_projects: number;
    completed_projects: number;
    projects_by_category: Record<string, number>;
    projects_by_academic_level: Record<string, number>;
}

interface FinancialStats {
    total_wallet_balance: number;
    total_transactions: number;
    total_deposits: number;
    total_withdrawals: number;
    pending_withdrawals: number;
    revenue_by_month: Array<{
        month: string;
        deposits: number;
        withdrawals: number;
    }>;
}

interface BidStats {
    total_bids: number;
    accepted_bids: number;
    pending_bids: number;
    rejected_bids: number;
    average_bid_amount: number;
}

interface RecentActivity {
    recent_users: Array<{
        id: number;
        name: string;
        email: string;
        created_at: string;
        role: string;
    }>;
    recent_projects: Array<{
        id: number;
        title: string;
        user: { name: string };
        status: string;
        created_at: string;
    }>;
    recent_transactions: Array<{
        id: number;
        user: { name: string };
        type: string;
        amount: number;
        status: string;
        created_at: string;
    }>;
}

interface HealthMetrics {
    project_completion_rate: number;
    user_verification_rate: number;
    bid_acceptance_rate: number;
    average_project_value: number;
}

interface Props {
    userStats: UserStats;
    projectStats: ProjectStats;
    financialStats: FinancialStats;
    bidStats: BidStats;
    recentActivity: RecentActivity;
    healthMetrics: HealthMetrics;
    filters: {
        start_date: string;
        end_date: string;
    };
}

export default function Analytics({
    userStats,
    projectStats,
    financialStats,
    bidStats,
    recentActivity,
    healthMetrics,
    filters
}: Props) {
    const { data, setData } = useForm({
        start_date: filters.start_date,
        end_date: filters.end_date,
    });

    const handleFilterChange = () => {
        router.get(route('admin.analytics.index'), data, {
            preserveState: true,
            replace: true,
        });
    };

    const formatCurrency = (amount: number) => `₵${Number(amount || 0).toFixed(2)}`;
    const formatPercentage = (value: number) => `${Number(value || 0).toFixed(1)}%`;

    return (
        <AppLayout>
            <Head title="Analytics Dashboard" />

            <div className="py-12">
                <div className="mx-auto max-w-7xl sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <h1 className="flex items-center gap-2 text-3xl font-bold text-gray-900">
                            <BarChart3 className="h-8 w-8 text-blue-600" />
                            Analytics Dashboard
                        </h1>
                        <p className="mt-2 text-gray-600">Platform insights and performance metrics</p>
                    </div>

                    {/* Date Filter */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Filter className="h-5 w-5" />
                                Date Range Filter
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-end gap-4">
                                <div>
                                    <Label htmlFor="start_date">Start Date</Label>
                                    <Input
                                        id="start_date"
                                        type="date"
                                        value={data.start_date}
                                        onChange={(e) => setData('start_date', e.target.value)}
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="end_date">End Date</Label>
                                    <Input
                                        id="end_date"
                                        type="date"
                                        value={data.end_date}
                                        onChange={(e) => setData('end_date', e.target.value)}
                                    />
                                </div>
                                <Button onClick={handleFilterChange}>
                                    Apply Filter
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Key Metrics */}
                    <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm text-gray-600">Total Users</p>
                                        <p className="text-3xl font-bold text-blue-600">{userStats.total_users}</p>
                                        <p className="text-xs text-green-600">+{userStats.new_users_this_month} this month</p>
                                    </div>
                                    <Users className="h-12 w-12 text-blue-600" />
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm text-gray-600">Total Projects</p>
                                        <p className="text-3xl font-bold text-green-600">{projectStats.total_projects}</p>
                                        <p className="text-xs text-gray-500">{projectStats.open_projects} open</p>
                                    </div>
                                    <FolderOpen className="h-12 w-12 text-green-600" />
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm text-gray-600">Total Wallet Balance</p>
                                        <p className="text-3xl font-bold text-purple-600">{formatCurrency(financialStats.total_wallet_balance)}</p>
                                        <p className="text-xs text-gray-500">{financialStats.total_transactions} transactions</p>
                                    </div>
                                    <DollarSign className="h-12 w-12 text-purple-600" />
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm text-gray-600">Total Bids</p>
                                        <p className="text-3xl font-bold text-orange-600">{bidStats.total_bids}</p>
                                        <p className="text-xs text-gray-500">{bidStats.accepted_bids} accepted</p>
                                    </div>
                                    <Target className="h-12 w-12 text-orange-600" />
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Health Metrics */}
                    <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
                        <Card>
                            <CardHeader className="pb-2">
                                <CardTitle className="text-sm">Project Completion Rate</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-green-600">
                                    {formatPercentage(healthMetrics.project_completion_rate)}
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="pb-2">
                                <CardTitle className="text-sm">User Verification Rate</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-blue-600">
                                    {formatPercentage(healthMetrics.user_verification_rate)}
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="pb-2">
                                <CardTitle className="text-sm">Bid Acceptance Rate</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-purple-600">
                                    {formatPercentage(healthMetrics.bid_acceptance_rate)}
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="pb-2">
                                <CardTitle className="text-sm">Avg Project Value</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-orange-600">
                                    {formatCurrency(healthMetrics.average_project_value)}
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Detailed Statistics */}
                    <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
                        {/* User Statistics */}
                        <Card>
                            <CardHeader>
                                <CardTitle>User Statistics</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="text-center rounded-lg bg-green-50 p-3">
                                        <p className="text-2xl font-bold text-green-600">{userStats.active_users}</p>
                                        <p className="text-sm text-green-700">Active Users</p>
                                    </div>
                                    <div className="text-center rounded-lg bg-blue-50 p-3">
                                        <p className="text-2xl font-bold text-blue-600">{userStats.verified_users}</p>
                                        <p className="text-sm text-blue-700">Verified Users</p>
                                    </div>
                                </div>
                                
                                <div>
                                    <h4 className="mb-2 font-medium">Users by Role</h4>
                                    <div className="space-y-2">
                                        {Object.entries(userStats.users_by_role).map(([role, count]) => (
                                            <div key={role} className="flex justify-between">
                                                <span className="capitalize">{role.replace('_', ' ')}</span>
                                                <Badge variant="outline">{count}</Badge>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Project Statistics */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Project Statistics</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-3 gap-2">
                                    <div className="text-center rounded-lg bg-yellow-50 p-3">
                                        <p className="text-lg font-bold text-yellow-600">{projectStats.open_projects}</p>
                                        <p className="text-xs text-yellow-700">Open</p>
                                    </div>
                                    <div className="text-center rounded-lg bg-blue-50 p-3">
                                        <p className="text-lg font-bold text-blue-600">{projectStats.in_progress_projects}</p>
                                        <p className="text-xs text-blue-700">In Progress</p>
                                    </div>
                                    <div className="text-center rounded-lg bg-green-50 p-3">
                                        <p className="text-lg font-bold text-green-600">{projectStats.completed_projects}</p>
                                        <p className="text-xs text-green-700">Completed</p>
                                    </div>
                                </div>

                                <div>
                                    <h4 className="mb-2 font-medium">Top Categories</h4>
                                    <div className="space-y-2">
                                        {Object.entries(projectStats.projects_by_category).slice(0, 5).map(([category, count]) => (
                                            <div key={category} className="flex justify-between">
                                                <span className="capitalize">{category}</span>
                                                <Badge variant="outline">{count}</Badge>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Recent Activity */}
                    <div className="mt-8">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Activity className="h-5 w-5" />
                                    Recent Activity
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                                    {/* Recent Users */}
                                    <div>
                                        <h4 className="mb-3 font-medium">Recent Users</h4>
                                        <div className="space-y-2">
                                            {recentActivity.recent_users.map((user) => (
                                                <div key={user.id} className="flex items-center justify-between rounded-lg bg-gray-50 p-2">
                                                    <div>
                                                        <p className="text-sm font-medium">{user.name}</p>
                                                        <p className="text-xs text-gray-500">{user.role}</p>
                                                    </div>
                                                    <Badge variant="outline" className="text-xs">
                                                        {new Date(user.created_at).toLocaleDateString()}
                                                    </Badge>
                                                </div>
                                            ))}
                                        </div>
                                    </div>

                                    {/* Recent Projects */}
                                    <div>
                                        <h4 className="mb-3 font-medium">Recent Projects</h4>
                                        <div className="space-y-2">
                                            {recentActivity.recent_projects.map((project) => (
                                                <div key={project.id} className="rounded-lg bg-gray-50 p-2">
                                                    <p className="text-sm font-medium">{project.title}</p>
                                                    <p className="text-xs text-gray-500">by {project.user.name}</p>
                                                    <div className="mt-1 flex justify-between">
                                                        <Badge variant="outline" className="text-xs">{project.status}</Badge>
                                                        <span className="text-xs text-gray-400">
                                                            {new Date(project.created_at).toLocaleDateString()}
                                                        </span>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>

                                    {/* Recent Transactions */}
                                    <div>
                                        <h4 className="mb-3 font-medium">Recent Transactions</h4>
                                        <div className="space-y-2">
                                            {recentActivity.recent_transactions.slice(0, 5).map((transaction) => (
                                                <div key={transaction.id} className="rounded-lg bg-gray-50 p-2">
                                                    <div className="flex justify-between">
                                                        <p className="text-sm font-medium">{transaction.user.name}</p>
                                                        <span className={`text-sm font-medium ${
                                                            transaction.type === 'deposit' ? 'text-green-600' : 'text-red-600'
                                                        }`}>
                                                            {transaction.type === 'deposit' ? '+' : '-'}{formatCurrency(transaction.amount)}
                                                        </span>
                                                    </div>
                                                    <div className="flex justify-between">
                                                        <Badge variant="outline" className="text-xs">{transaction.type}</Badge>
                                                        <span className="text-xs text-gray-400">
                                                            {new Date(transaction.created_at).toLocaleDateString()}
                                                        </span>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
