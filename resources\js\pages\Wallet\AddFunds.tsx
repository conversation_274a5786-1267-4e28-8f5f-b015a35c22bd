import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthenticatedLayout from '@/layouts/app-layout';
import { Head, useForm } from '@inertiajs/react';
import { AlertCircle, CheckCircle, CreditCard, Plus, Wallet } from 'lucide-react';
import React, { useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
    wallet_balance: number;
}

interface Props {
    user: User;
    paystack_public_key: string;
}

export default function AddFunds({ user, paystack_public_key }: Props) {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');

    const { data, setData, post, processing, errors, reset } = useForm({
        amount: '',
    });

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError('');
        setIsLoading(true);

        try {
            const response = await fetch(route('wallet.initialize-payment'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({ amount: parseFloat(data.amount) }),
            });

            const result = await response.json();

            if (result.status === 'success') {
                // Redirect to Paystack payment page
                window.location.href = result.authorization_url;
            } else {
                setError(result.message || 'Payment initialization failed');
            }
        } catch (error) {
            setError('Network error. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    const quickAmounts = [50, 100, 200, 500, 1000];

    return (
        <AuthenticatedLayout>
            <Head title="Add Funds" />

            <div className="py-12">
                <div className="mx-auto max-w-4xl sm:px-6 lg:px-8">
                    <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
                        {/* Current Balance Card */}
                        <div className="lg:col-span-1">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Wallet className="h-5 w-5 text-blue-600" />
                                        Current Balance
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-3xl font-bold text-blue-600">₵{Number(user.wallet_balance || 0).toFixed(2)}</div>
                                    <p className="mt-2 text-sm text-gray-600">Available in your wallet</p>
                                </CardContent>
                            </Card>

                            {/* PayStack Info */}
                            <Card className="mt-6">
                                <CardHeader>
                                    <CardTitle className="text-sm">Secure Payment</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="flex items-center gap-2 text-sm text-gray-600">
                                        <CheckCircle className="h-4 w-4 text-green-500" />
                                        Powered by PayStack
                                    </div>
                                    <div className="mt-2 flex items-center gap-2 text-sm text-gray-600">
                                        <CheckCircle className="h-4 w-4 text-green-500" />
                                        SSL Encrypted
                                    </div>
                                    <div className="mt-2 flex items-center gap-2 text-sm text-gray-600">
                                        <CheckCircle className="h-4 w-4 text-green-500" />
                                        Bank-level Security
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Add Funds Form */}
                        <div className="lg:col-span-2">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Plus className="h-5 w-5 text-green-600" />
                                        Add Funds to Wallet
                                    </CardTitle>
                                    <CardDescription>Top up your wallet to bid on projects and make payments</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    {/* Flash Messages */}
                                    {window.location.search.includes('success') && (
                                        <Alert className="mb-6 border-green-200 bg-green-50">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            <AlertDescription className="text-green-800">
                                                Payment successful! Your wallet has been credited.
                                            </AlertDescription>
                                        </Alert>
                                    )}

                                    {(error || window.location.search.includes('error')) && (
                                        <Alert className="mb-6 border-red-200 bg-red-50">
                                            <AlertCircle className="h-4 w-4 text-red-600" />
                                            <AlertDescription className="text-red-800">
                                                {error || 'Payment failed. Please try again.'}
                                            </AlertDescription>
                                        </Alert>
                                    )}

                                    <form onSubmit={handleSubmit} className="space-y-6">
                                        {/* Quick Amount Selection */}
                                        <div>
                                            <Label className="text-sm font-medium">Quick Select Amount</Label>
                                            <div className="mt-2 grid grid-cols-5 gap-2">
                                                {quickAmounts.map((amount) => (
                                                    <button
                                                        key={amount}
                                                        type="button"
                                                        onClick={() => setData('amount', amount.toString())}
                                                        className={`rounded-md border px-3 py-2 text-sm transition-colors ${
                                                            data.amount === amount.toString()
                                                                ? 'border-blue-500 bg-blue-50 text-blue-700'
                                                                : 'border-gray-300 hover:border-gray-400'
                                                        }`}
                                                    >
                                                        ₵{amount}
                                                    </button>
                                                ))}
                                            </div>
                                        </div>

                                        {/* Custom Amount Input */}
                                        <div>
                                            <Label htmlFor="amount" className="text-sm font-medium">
                                                Amount (₵)
                                            </Label>
                                            <div className="relative mt-2">
                                                <Input
                                                    id="amount"
                                                    type="number"
                                                    min="10"
                                                    max="50000"
                                                    step="0.01"
                                                    value={data.amount}
                                                    onChange={(e) => setData('amount', e.target.value)}
                                                    placeholder="Enter amount"
                                                    className="pl-8"
                                                    required
                                                />
                                                <span className="absolute top-1/2 left-3 -translate-y-1/2 transform text-gray-500">₵</span>
                                            </div>
                                            {errors.amount && <p className="mt-1 text-sm text-red-600">{errors.amount}</p>}
                                            <p className="mt-1 text-xs text-gray-500">Minimum: ₵10 | Maximum: ₵50,000</p>
                                        </div>

                                        {/* Payment Method Info */}
                                        <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                                            <div className="flex items-start gap-3">
                                                <CreditCard className="mt-0.5 h-5 w-5 text-blue-600" />
                                                <div>
                                                    <h4 className="font-medium text-blue-900">Payment via PayStack</h4>
                                                    <p className="mt-1 text-sm text-blue-700">
                                                        You'll be redirected to PayStack's secure payment page to complete your transaction using:
                                                    </p>
                                                    <ul className="mt-2 space-y-1 text-sm text-blue-700">
                                                        <li>• Bank Transfer</li>
                                                        <li>• Debit/Credit Cards</li>
                                                        <li>• Mobile Money</li>
                                                        <li>• Bank USSD</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Submit Button */}
                                        <Button type="submit" disabled={isLoading || processing || !data.amount} className="w-full" size="lg">
                                            {isLoading || processing ? (
                                                <>
                                                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                                                    Processing...
                                                </>
                                            ) : (
                                                <>
                                                    <Plus className="mr-2 h-4 w-4" />
                                                    Add ₵{data.amount || '0'} to Wallet
                                                </>
                                            )}
                                        </Button>
                                    </form>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}
