import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import { Head, Link } from '@inertiajs/react';
import { format } from 'date-fns';
import { ArrowDownLeft, ArrowUpRight, Clock, DollarSign, Filter } from 'lucide-react';

interface User {
    id: number;
    name: string;
    email: string;
    wallet_balance: number;
}

interface Transaction {
    id: number;
    transaction_id: string;
    type: 'deposit' | 'withdrawal' | 'payment' | 'refund' | 'commission';
    amount: number;
    balance_before: number;
    balance_after: number;
    status: 'pending' | 'completed' | 'failed' | 'cancelled';
    payment_method: string;
    description: string;
    created_at: string;
}

interface PaginatedTransactions {
    data: Transaction[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    links: any[];
}

interface Props {
    transactions: PaginatedTransactions;
    user: User;
}

export default function Transactions({ transactions, user }: Props) {
    const getTransactionIcon = (type: string) => {
        switch (type) {
            case 'deposit':
                return <ArrowDownLeft className="h-4 w-4 text-green-600" />;
            case 'withdrawal':
                return <ArrowUpRight className="h-4 w-4 text-red-600" />;
            case 'payment':
                return <ArrowUpRight className="h-4 w-4 text-blue-600" />;
            case 'refund':
                return <ArrowDownLeft className="h-4 w-4 text-green-600" />;
            case 'commission':
                return <ArrowUpRight className="h-4 w-4 text-purple-600" />;
            default:
                return <DollarSign className="h-4 w-4 text-gray-600" />;
        }
    };

    const getTransactionColor = (type: string) => {
        switch (type) {
            case 'deposit':
            case 'refund':
                return 'text-green-600';
            case 'withdrawal':
            case 'payment':
            case 'commission':
                return 'text-red-600';
            default:
                return 'text-gray-600';
        }
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            completed: 'bg-green-100 text-green-800',
            pending: 'bg-yellow-100 text-yellow-800',
            failed: 'bg-red-100 text-red-800',
            cancelled: 'bg-gray-100 text-gray-800',
        };

        return (
            <Badge className={variants[status as keyof typeof variants] || variants.cancelled}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
        );
    };

    const formatAmount = (amount: number, type: string) => {
        const prefix = ['deposit', 'refund'].includes(type) ? '+' : '-';
        return `${prefix}₵${Math.abs(amount).toFixed(2)}`;
    };

    return (
        <AppLayout>
            <Head title="Transaction History" />

            <div className="mx-auto max-w-7xl p-6">
                {/* Header */}
                <div className="mb-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900">Transaction History</h1>
                            <p className="mt-2 text-gray-600">Track all your wallet activities and transactions</p>
                        </div>
                        <div className="flex items-center gap-4">
                            <Link
                                href="/wallet/add-funds"
                                className="inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-xs font-semibold tracking-widest text-white uppercase transition duration-150 ease-in-out hover:bg-blue-700 focus:bg-blue-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none active:bg-blue-900"
                            >
                                <ArrowDownLeft className="mr-2 h-4 w-4" />
                                Add Funds
                            </Link>
                        </div>
                    </div>
                </div>

                {/* Wallet Balance Card */}
                <Card className="mb-6">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Current Wallet Balance</p>
                                <p className="text-3xl font-bold text-gray-900">₵{user.wallet_balance.toFixed(2)}</p>
                            </div>
                            <div className="rounded-full bg-blue-100 p-3">
                                <DollarSign className="h-8 w-8 text-blue-600" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Transactions */}
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <CardTitle className="flex items-center gap-2">
                                <Clock className="h-5 w-5" />
                                Recent Transactions
                            </CardTitle>
                            <Button variant="outline" size="sm">
                                <Filter className="mr-2 h-4 w-4" />
                                Filter
                            </Button>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {transactions.data.length === 0 ? (
                            <div className="py-12 text-center">
                                <Clock className="mx-auto mb-4 h-16 w-16 text-gray-300" />
                                <h3 className="mb-2 text-lg font-medium text-gray-900">No transactions yet</h3>
                                <p className="mb-6 text-gray-600">Your transaction history will appear here once you start using your wallet.</p>
                                <Link
                                    href="/wallet/add-funds"
                                    className="inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-xs font-semibold tracking-widest text-white uppercase transition duration-150 ease-in-out hover:bg-blue-700 focus:bg-blue-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none active:bg-blue-900"
                                >
                                    Add Your First Funds
                                </Link>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                {transactions.data.map((transaction) => (
                                    <div
                                        key={transaction.id}
                                        className="flex items-center justify-between rounded-lg border p-4 transition-colors hover:bg-gray-50"
                                    >
                                        <div className="flex items-center gap-4">
                                            <div className="rounded-full bg-gray-100 p-2">{getTransactionIcon(transaction.type)}</div>
                                            <div>
                                                <div className="flex items-center gap-2">
                                                    <p className="font-medium text-gray-900">{transaction.description}</p>
                                                    {getStatusBadge(transaction.status)}
                                                </div>
                                                <div className="mt-1 flex items-center gap-4">
                                                    <p className="text-sm text-gray-600">
                                                        {format(new Date(transaction.created_at), 'MMM dd, yyyy HH:mm')}
                                                    </p>
                                                    <p className="text-sm text-gray-600">ID: {transaction.transaction_id}</p>
                                                    {transaction.payment_method && (
                                                        <p className="text-sm text-gray-600">via {transaction.payment_method}</p>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <p className={`font-semibold ${getTransactionColor(transaction.type)}`}>
                                                {formatAmount(transaction.amount, transaction.type)}
                                            </p>
                                            <p className="text-sm text-gray-600">Balance: ₵{transaction.balance_after.toFixed(2)}</p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}

                        {/* Pagination */}
                        {transactions.data.length > 0 && transactions.last_page > 1 && (
                            <>
                                <Separator className="my-6" />
                                <div className="flex items-center justify-between">
                                    <p className="text-sm text-gray-600">
                                        Showing {(transactions.current_page - 1) * transactions.per_page + 1} to{' '}
                                        {Math.min(transactions.current_page * transactions.per_page, transactions.total)} of {transactions.total}{' '}
                                        transactions
                                    </p>
                                    <div className="flex items-center gap-2">
                                        {transactions.links.map((link, index) =>
                                            link.url ? (
                                                <Link
                                                    key={index}
                                                    href={link.url}
                                                    className={`rounded border px-3 py-1 text-sm ${
                                                        link.active
                                                            ? 'border-blue-600 bg-blue-600 text-white'
                                                            : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                                                    }`}
                                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                                />
                                            ) : (
                                                <span
                                                    key={index}
                                                    className="rounded border border-gray-200 px-3 py-1 text-sm text-gray-400"
                                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                                />
                                            ),
                                        )}
                                    </div>
                                </div>
                            </>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
