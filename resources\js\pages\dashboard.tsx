import ProjectCard from '@/components/project-card';
import { Button } from '@/components/ui/button';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { Plus } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

interface Project {
    id: number;
    title: string;
    slug?: string;
    description: string;
    budget_min?: number;
    budget_max?: number;
    budget_type: 'fixed' | 'hourly' | 'negotiable';
    deadline?: string;
    category?: string;
    academic_level?: string;
    status: 'open' | 'in_progress' | 'completed' | 'cancelled';
    file_count: number;
    created_at: string;
}

interface DashboardProps {
    projects: Project[];
}

export default function Dashboard({ projects = [] }: DashboardProps) {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold">My Projects</h1>
                        <p className="text-muted-foreground">Manage your posted projects and track their progress</p>
                    </div>
                    <Button asChild>
                        <Link href="/projects/create">
                            <Plus className="mr-2 h-4 w-4" />
                            Post New Project
                        </Link>
                    </Button>
                </div>

                {/* Projects Grid */}
                {projects.length > 0 ? (
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                        {projects.map((project) => (
                            <ProjectCard key={project.id} project={project} variant="dashboard" />
                        ))}
                    </div>
                ) : (
                    <div className="py-12 text-center">
                        <div className="mx-auto max-w-md">
                            <div className="mx-auto h-12 w-12 text-muted-foreground/50">
                                <Plus className="h-full w-full" />
                            </div>
                            <h3 className="mt-4 text-lg font-semibold">No projects yet</h3>
                            <p className="mt-2 text-muted-foreground">
                                Get started by posting your first project. Connect with talented researchers and get your academic work done
                                professionally.
                            </p>
                            <Button className="mt-4" asChild>
                                <Link href="/projects/create">
                                    <Plus className="mr-2 h-4 w-4" />
                                    Post Your First Project
                                </Link>
                            </Button>
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
