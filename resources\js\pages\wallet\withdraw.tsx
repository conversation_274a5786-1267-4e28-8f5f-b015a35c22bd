import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AuthenticatedLayout from '@/layouts/app-layout';
import { Head, useForm } from '@inertiajs/react';
import { AlertCircle, ArrowLeft, CreditCard, Minus, Wallet } from 'lucide-react';
import React from 'react';

interface User {
    id: number;
    name: string;
    email: string;
    wallet_balance: number;
}

interface Props {
    user: User;
}

export default function Withdraw({ user }: Props) {
    const { data, setData, post, processing, errors, reset } = useForm({
        amount: '',
        bank_name: '',
        account_number: '',
        account_name: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('funds.process-withdraw'), {
            onSuccess: () => {
                reset();
            },
        });
    };

    const quickAmounts = [50, 100, 200, 500];
    const maxWithdrawable = Math.max(0, user.wallet_balance - 5); // Keep minimum ₵5

    const popularBanks = [
        'Access Bank',
        'Ecobank Ghana',
        'Fidelity Bank',
        'First National Bank',
        'GCB Bank',
        'GT Bank',
        'National Investment Bank',
        'Prudential Bank',
        'Republic Bank',
        'Stanbic Bank',
        'Standard Chartered Bank',
        'UMB Bank',
        'Zenith Bank',
    ];

    return (
        <AuthenticatedLayout>
            <Head title="Withdraw Funds" />

            <div className="py-12">
                <div className="mx-auto max-w-4xl sm:px-6 lg:px-8">
                    {/* Back Button */}
                    <div className="mb-6">
                        <Button variant="ghost" onClick={() => window.history.back()}>
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back
                        </Button>
                    </div>

                    <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
                        {/* Current Balance Card */}
                        <div className="lg:col-span-1">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Wallet className="h-5 w-5 text-blue-600" />
                                        Current Balance
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-3xl font-bold text-blue-600">₵{Number(user.wallet_balance || 0).toFixed(2)}</div>
                                    <p className="mt-2 text-sm text-gray-600">Available in your wallet</p>

                                    <div className="mt-4 rounded-lg bg-yellow-50 p-3">
                                        <p className="text-sm text-yellow-800">
                                            <strong>Maximum withdrawable:</strong> ₵{maxWithdrawable.toFixed(2)}
                                        </p>
                                        <p className="mt-1 text-xs text-yellow-600">(₵5 minimum balance required)</p>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Important Notes */}
                            <Card className="mt-6">
                                <CardHeader>
                                    <CardTitle className="text-sm">Important Notes</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-2 text-sm text-gray-600">
                                    <p>• Withdrawals are processed within 1-3 business days</p>
                                    <p>• Minimum withdrawal amount: ₵10</p>
                                    <p>• You must maintain ₵5 minimum balance</p>
                                    <p>• Ensure bank details are accurate to avoid delays</p>
                                    <p>• Contact support for any withdrawal issues</p>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Withdrawal Form */}
                        <div className="lg:col-span-2">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <CreditCard className="h-5 w-5 text-red-600" />
                                        Withdraw Funds
                                    </CardTitle>
                                    <CardDescription>
                                        Request a withdrawal to your bank account. All withdrawals are manually reviewed for security.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <form onSubmit={handleSubmit} className="space-y-6">
                                        {/* Amount Section */}
                                        <div className="space-y-4">
                                            <div>
                                                <Label htmlFor="amount">Withdrawal Amount (₵)</Label>
                                                <Input
                                                    id="amount"
                                                    type="number"
                                                    step="0.01"
                                                    min="10"
                                                    max={maxWithdrawable}
                                                    placeholder="Enter amount to withdraw"
                                                    value={data.amount}
                                                    onChange={(e) => setData('amount', e.target.value)}
                                                    className={errors.amount ? 'border-red-500' : ''}
                                                />
                                                {errors.amount && <p className="mt-1 text-sm text-red-600">{errors.amount}</p>}
                                            </div>

                                            {/* Quick Amount Buttons */}
                                            <div>
                                                <Label className="text-sm text-gray-600">Quick amounts:</Label>
                                                <div className="mt-2 flex flex-wrap gap-2">
                                                    {quickAmounts.map((amount) => (
                                                        <Button
                                                            key={amount}
                                                            type="button"
                                                            variant="outline"
                                                            size="sm"
                                                            disabled={amount > maxWithdrawable}
                                                            onClick={() => setData('amount', amount.toString())}
                                                        >
                                                            ₵{amount}
                                                        </Button>
                                                    ))}
                                                    <Button
                                                        type="button"
                                                        variant="outline"
                                                        size="sm"
                                                        disabled={maxWithdrawable <= 0}
                                                        onClick={() => setData('amount', maxWithdrawable.toFixed(2))}
                                                    >
                                                        Max (₵{maxWithdrawable.toFixed(2)})
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Bank Details Section */}
                                        <div className="space-y-4">
                                            <h3 className="text-lg font-medium">Bank Account Details</h3>

                                            <div>
                                                <Label htmlFor="bank_name">Bank Name</Label>
                                                <Select value={data.bank_name} onValueChange={(value) => setData('bank_name', value)}>
                                                    <SelectTrigger className={errors.bank_name ? 'border-red-500' : ''}>
                                                        <SelectValue placeholder="Select your bank" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {popularBanks.map((bank) => (
                                                            <SelectItem key={bank} value={bank}>
                                                                {bank}
                                                            </SelectItem>
                                                        ))}
                                                        <SelectItem value="Other">Other Bank</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                                {errors.bank_name && <p className="mt-1 text-sm text-red-600">{errors.bank_name}</p>}
                                            </div>

                                            {data.bank_name === 'Other' && (
                                                <div>
                                                    <Label htmlFor="custom_bank">Bank Name</Label>
                                                    <Input
                                                        id="custom_bank"
                                                        placeholder="Enter your bank name"
                                                        value={data.bank_name === 'Other' ? '' : data.bank_name}
                                                        onChange={(e) => setData('bank_name', e.target.value)}
                                                    />
                                                </div>
                                            )}

                                            <div>
                                                <Label htmlFor="account_number">Account Number</Label>
                                                <Input
                                                    id="account_number"
                                                    placeholder="Enter your account number"
                                                    value={data.account_number}
                                                    onChange={(e) => setData('account_number', e.target.value)}
                                                    className={errors.account_number ? 'border-red-500' : ''}
                                                />
                                                {errors.account_number && <p className="mt-1 text-sm text-red-600">{errors.account_number}</p>}
                                            </div>

                                            <div>
                                                <Label htmlFor="account_name">Account Name</Label>
                                                <Input
                                                    id="account_name"
                                                    placeholder="Enter account holder name"
                                                    value={data.account_name}
                                                    onChange={(e) => setData('account_name', e.target.value)}
                                                    className={errors.account_name ? 'border-red-500' : ''}
                                                />
                                                {errors.account_name && <p className="mt-1 text-sm text-red-600">{errors.account_name}</p>}
                                                <p className="mt-1 text-xs text-gray-500">Must match the name on your bank account</p>
                                            </div>
                                        </div>

                                        {/* Error Alert */}
                                        {(() => {
                                            // Some backends return a generic error string in different keys
                                            const genericError = (errors as any)?.error || (errors as any)?.message || (errors as any)?.general;
                                            if (typeof genericError === 'string' && genericError.length) {
                                                return (
                                                    <Alert variant="destructive">
                                                        <AlertCircle className="h-4 w-4" />
                                                        <AlertDescription>{genericError}</AlertDescription>
                                                    </Alert>
                                                );
                                            }

                                            return null;
                                        })()}

                                        {/* Submit Button */}
                                        <Button
                                            type="submit"
                                            disabled={
                                                processing ||
                                                !data.amount ||
                                                !data.bank_name ||
                                                !data.account_number ||
                                                !data.account_name ||
                                                maxWithdrawable <= 0
                                            }
                                            className="w-full"
                                            size="lg"
                                        >
                                            {processing ? (
                                                <>
                                                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                                                    Processing...
                                                </>
                                            ) : (
                                                <>
                                                    <Minus className="mr-2 h-4 w-4" />
                                                    Request Withdrawal of ₵{data.amount || '0'}
                                                </>
                                            )}
                                        </Button>
                                    </form>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}
