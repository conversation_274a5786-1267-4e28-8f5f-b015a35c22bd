import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { Award, BookOpen, Users } from 'lucide-react';

export default function Welcome() {
    const { auth } = usePage<SharedData>().props;

    return (
        <>
            <Head title="Welcome">
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>
            <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800">
                {/* Navigation */}
                <header className="relative z-10 w-full">
                    <nav className="mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8">
                        <div className="flex items-center gap-2">
                            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-white">
                                <BookOpen className="h-5 w-5" />
                            </div>
                            <span className="text-xl font-semibold text-gray-900 dark:text-white">Thesylink</span>
                        </div>
                        <div className="flex items-center gap-4">
                            {auth.user ? (
                                <Link
                                    href={route('dashboard')}
                                    className="rounded-lg bg-primary px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-primary/90"
                                >
                                    Dashboard
                                </Link>
                            ) : (
                                <>
                                    <Link
                                        href={route('membership')}
                                        className="text-sm font-medium text-gray-700 transition-colors hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                                    >
                                        Pricing
                                    </Link>
                                    <Link
                                        href={route('login')}
                                        className="text-sm font-medium text-gray-700 transition-colors hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                                    >
                                        Log in
                                    </Link>
                                    <Link
                                        href={route('register')}
                                        className="rounded-lg bg-primary px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-primary/90"
                                    >
                                        Get Started
                                    </Link>
                                </>
                            )}
                        </div>
                    </nav>
                </header>
                {/* Hero Section */}
                <main className="relative">
                    <div className="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
                        <div className="mx-auto max-w-2xl text-center">
                            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl dark:text-white">
                                Connect Students with
                                <span className="text-primary"> Academic Excellence</span>
                            </h1>
                            <p className="mt-6 text-lg leading-8 text-gray-600 dark:text-gray-300">
                                Bridge the gap between students and skilled academic freelancers. Get quality research projects, academic writing, and
                                expert guidance to achieve your educational goals.
                            </p>
                            <div className="mt-10 flex items-center justify-center gap-x-6">
                                <Link
                                    href={route('register')}
                                    className="rounded-lg bg-primary px-6 py-3 text-sm font-semibold text-white shadow-sm transition-all hover:bg-primary/90 hover:shadow-lg"
                                >
                                    Get Started Today
                                </Link>
                                <Link
                                    href={route('login')}
                                    className="text-sm leading-6 font-semibold text-gray-900 transition-colors hover:text-primary dark:text-white dark:hover:text-primary"
                                >
                                    Sign In <span aria-hidden="true">→</span>
                                </Link>
                            </div>
                        </div>
                    </div>

                    {/* Hero Image */}
                    <div className="relative mx-auto max-w-5xl px-6 lg:px-8">
                        <div className="relative overflow-hidden rounded-2xl bg-white shadow-2xl dark:bg-gray-800">
                            <img
                                src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80"
                                alt="Students collaborating on academic projects"
                                className="h-[400px] w-full object-cover sm:h-[500px]"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                        </div>
                    </div>
                </main>

                {/* Features Section */}
                <section className="py-24 sm:py-32">
                    <div className="mx-auto max-w-7xl px-6 lg:px-8">
                        <div className="mx-auto max-w-2xl text-center">
                            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl dark:text-white">How Thesylink Works</h2>
                            <p className="mt-4 text-lg leading-8 text-gray-600 dark:text-gray-300">
                                Simple steps to connect with academic professionals and get the help you need.
                            </p>
                        </div>
                        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
                            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
                                <div className="flex flex-col">
                                    <dt className="flex items-center gap-x-3 text-base leading-7 font-semibold text-gray-900 dark:text-white">
                                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary">
                                            <BookOpen className="h-6 w-6 text-white" />
                                        </div>
                                        Post Your Project
                                    </dt>
                                    <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600 dark:text-gray-300">
                                        <p className="flex-auto">
                                            Describe your academic project, research needs, or writing requirements. Set your budget and timeline to
                                            attract qualified freelancers.
                                        </p>
                                        <p className="mt-6">
                                            <Link
                                                href="/projects/create"
                                                className="text-sm leading-6 font-semibold text-primary hover:text-primary/80"
                                            >
                                                Create Project <span aria-hidden="true">→</span>
                                            </Link>
                                        </p>
                                    </dd>
                                </div>
                                <div className="flex flex-col">
                                    <dt className="flex items-center gap-x-3 text-base leading-7 font-semibold text-gray-900 dark:text-white">
                                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-secondary">
                                            <Users className="h-6 w-6 text-white" />
                                        </div>
                                        Connect with Experts
                                    </dt>
                                    <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600 dark:text-gray-300">
                                        <p className="flex-auto">
                                            Browse proposals from verified academic freelancers. Review their profiles, ratings, and previous work to
                                            find the perfect match.
                                        </p>
                                        <p className="mt-6">
                                            <Link href="/projects" className="text-sm leading-6 font-semibold text-secondary hover:text-secondary/80">
                                                Browse Freelancers <span aria-hidden="true">→</span>
                                            </Link>
                                        </p>
                                    </dd>
                                </div>
                                <div className="flex flex-col">
                                    <dt className="flex items-center gap-x-3 text-base leading-7 font-semibold text-gray-900 dark:text-white">
                                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-accent">
                                            <Award className="h-6 w-6 text-white" />
                                        </div>
                                        Achieve Excellence
                                    </dt>
                                    <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600 dark:text-gray-300">
                                        <p className="flex-auto">
                                            Collaborate with your chosen freelancer, track progress, and receive high-quality academic work that meets
                                            your standards.
                                        </p>
                                        <p className="mt-6">
                                            <Link href="/register" className="text-sm leading-6 font-semibold text-accent hover:text-accent/80">
                                                Get Started <span aria-hidden="true">→</span>
                                            </Link>
                                        </p>
                                    </dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="bg-primary py-16 sm:py-24">
                    <div className="mx-auto max-w-7xl px-6 lg:px-8">
                        <div className="mx-auto max-w-2xl text-center">
                            <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">Ready to Get Started?</h2>
                            <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-blue-100">
                                Join thousands of students and freelancers who trust Thesylink for their academic projects.
                            </p>
                            <div className="mt-10 flex items-center justify-center gap-x-6">
                                <Link
                                    href={route('register')}
                                    className="rounded-lg bg-white px-6 py-3 text-sm font-semibold text-primary shadow-sm transition-all hover:bg-gray-50 hover:shadow-lg"
                                >
                                    Join as Student
                                </Link>
                                <Link
                                    href={route('register')}
                                    className="rounded-lg border border-white px-6 py-3 text-sm font-semibold text-white transition-all hover:bg-white hover:text-primary"
                                >
                                    Join as Freelancer
                                </Link>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Footer */}
                <footer className="bg-white dark:bg-gray-900">
                    <div className="mx-auto max-w-7xl px-6 py-12 lg:px-8">
                        <div className="flex items-center justify-center">
                            <div className="flex items-center gap-2">
                                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-white">
                                    <BookOpen className="h-5 w-5" />
                                </div>
                                <span className="text-xl font-semibold text-gray-900 dark:text-white">Thesylink</span>
                            </div>
                        </div>
                        <p className="mt-4 text-center text-sm leading-6 text-gray-600 dark:text-gray-400">
                            © 2025 Thesylink. Connecting students with academic excellence.
                        </p>
                    </div>
                </footer>
            </div>
        </>
    );
}
