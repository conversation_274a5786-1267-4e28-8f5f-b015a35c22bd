<?php

use App\Http\Controllers\BidController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\MilestoneController;
use App\Http\Controllers\ProjectController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Project routes
    Route::resource('projects', ProjectController::class);
    Route::get('browse', [ProjectController::class, 'index'])->name('browse');
    Route::get('my-projects', [DashboardController::class, 'myProjects'])->name('my-projects');

    // Bid routes
    Route::post('projects/{project}/bids', [BidController::class, 'store'])->name('bids.store');
    Route::patch('projects/{project}/bids/{bid}/accept', [BidController::class, 'accept'])->name('bids.accept');
    Route::patch('projects/{project}/bids/{bid}/reject', [BidController::class, 'reject'])->name('bids.reject');

    // Milestone routes
    Route::get('projects/{project}/milestones', [MilestoneController::class, 'index'])->name('milestones.index');
    Route::patch('projects/{project}/milestones/{milestone}/start', [MilestoneController::class, 'start'])->name('milestones.start');
    Route::patch('projects/{project}/milestones/{milestone}/submit', [MilestoneController::class, 'submit'])->name('milestones.submit');
    Route::patch('projects/{project}/milestones/{milestone}/approve', [MilestoneController::class, 'approve'])->name('milestones.approve');
    Route::patch('projects/{project}/milestones/{milestone}/revision', [MilestoneController::class, 'requestRevision'])->name('milestones.revision');

    // Chat routes
    Route::get('inbox', [ChatController::class, 'inbox'])->name('inbox');
    Route::get('api/conversations', [ChatController::class, 'index'])->name('conversations.index');
    Route::get('api/conversations/{conversation}', [ChatController::class, 'show'])->name('conversations.show');
    Route::post('api/conversations/{conversation}/messages', [ChatController::class, 'store'])->name('messages.store');
    Route::post('api/projects/{project}/conversations', [ChatController::class, 'createOrGet'])->name('conversations.create');

    // Rating routes
    Route::get('projects/{project}/rate', [App\Http\Controllers\RatingController::class, 'create'])->name('ratings.create');
    Route::post('projects/{project}/rate', [App\Http\Controllers\RatingController::class, 'store'])->name('ratings.store');
    Route::get('users/{user}/ratings', [App\Http\Controllers\RatingController::class, 'userRatings'])->name('users.ratings');

    // Wallet routes
    Route::prefix('wallet')->name('wallet.')->group(function () {
        Route::get('add-funds', [App\Http\Controllers\WalletController::class, 'addFunds'])->name('add-funds');
        Route::post('initialize-payment', [App\Http\Controllers\WalletController::class, 'initializePayment'])->name('initialize-payment');
        Route::get('verify', [App\Http\Controllers\WalletController::class, 'verifyPayment'])->name('verify');
        Route::get('transactions', [App\Http\Controllers\WalletController::class, 'transactions'])->name('transactions');
    });

    // Funds routes
    Route::prefix('funds')->name('funds.')->group(function () {
        Route::get('withdraw', [App\Http\Controllers\WalletController::class, 'withdraw'])->name('withdraw');
        Route::post('withdraw', [App\Http\Controllers\WalletController::class, 'processWithdraw'])->name('process-withdraw');
    });

    // Admin routes (Super Admin only)
    Route::prefix('admin')->name('admin.')->middleware('super_admin')->group(function () {
        Route::get('analytics', [App\Http\Controllers\Admin\AnalyticsController::class, 'index'])->name('analytics.index');
        Route::get('users', [App\Http\Controllers\Admin\UserManagementController::class, 'index'])->name('users.index');
        Route::get('users/{user}', [App\Http\Controllers\Admin\UserManagementController::class, 'show'])->name('users.show');
        Route::put('users/{user}', [App\Http\Controllers\Admin\UserManagementController::class, 'update'])->name('users.update');
        Route::put('users/{user}/status', [App\Http\Controllers\Admin\UserManagementController::class, 'updateStatus'])->name('users.update-status');
        Route::patch('users/{user}/verification', [App\Http\Controllers\Admin\UserManagementController::class, 'updateVerification'])->name('users.update-verification');
        Route::post('users/{user}/wallet', [App\Http\Controllers\Admin\UserManagementController::class, 'adjustWallet'])->name('users.adjust-wallet');
        Route::delete('users/{user}', [App\Http\Controllers\Admin\UserManagementController::class, 'destroy'])->name('users.destroy');
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
