{"__meta": {"id": "01K3ZE0PVB12PENGGJYNF7MVCF", "datetime": "2025-08-31 06:46:13", "utime": **********.10087, "method": "POST", "uri": "/_boost/browser-logs", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[06:46:13] LOG.error: Unhandled Promise Rejection Error Page not found: ./pages/Wallet/AddFunds.tsx Error: Page not found: ./pages/Wallet/AddFunds.tsx\n    at resolvePageComponent (http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=7660f6dd:12:9)\n    at resolve (http://[::1]:5173/resources/js/app.tsx:11:22)\n    at CurrentPage.resolveComponent (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=7660f6dd:11832:54)\n    at CurrentPage.resolve (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=7660f6dd:9554:33)\n    at CurrentPage.set (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=7660f6dd:9484:17)\n    at _Response.setPage (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=7660f6dd:10535:17)\n    at async _Response.process (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=7660f6dd:10462:5) {\n    \"url\": \"http:\\/\\/localhost:8000\\/dashboard\",\n    \"user_agent\": \"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\n    \"timestamp\": \"2025-08-31T06:46:12.304Z\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.073857, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 5, "start": 1756622772.722723, "end": **********.1009, "duration": 0.3781769275665283, "duration_str": "378ms", "measures": [{"label": "Booting", "start": 1756622772.722723, "relative_start": 0, "end": **********.030845, "relative_end": **********.030845, "duration": 0.***************, "duration_str": "308ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.030858, "relative_start": 0.****************, "end": **********.100904, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "70.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.063438, "relative_start": 0.****************, "end": **********.067643, "relative_end": **********.067643, "duration": 0.004204988479614258, "duration_str": "4.2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.097294, "relative_start": 0.****************, "end": **********.097537, "relative_end": **********.097537, "duration": 0.0002429485321044922, "duration_str": "243μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.09755, "relative_start": 0.*****************, "end": **********.097563, "relative_end": **********.097563, "duration": 1.3113021850585938e-05, "duration_str": "13μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "21MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 0, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionServiceProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php", "line": 52}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1153}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 971}], "start": **********.090962, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure", "uri": "POST _boost/browser-logs", "excluded_middleware": ["Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken"], "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fboost%2Fsrc%2FBoostServiceProvider.php&line=100\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/laravel/boost/src/BoostServiceProvider.php:100-126</a>", "duration": "379ms", "peak_memory": "22MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-421701260 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-421701260\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>logs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n      \"<span class=sf-dump-key>timestamp</span>\" => \"<span class=sf-dump-str title=\"24 characters\">2025-08-31T06:46:12.304Z</span>\"\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Unhandled Promise Rejection</span>\"\n          \"<span class=sf-dump-key>reason</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Error</span>\"\n            \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Page not found: ./pages/Wallet/AddFunds.tsx</span>\"\n            \"<span class=sf-dump-key>stack</span>\" => \"\"\"\n              <span class=sf-dump-str title=\"798 characters\">Error: Page not found: ./pages/Wallet/AddFunds.tsx<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"798 characters\">    at resolvePageComponent (http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=7660f6dd:12:9)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"798 characters\">    at resolve (http://[::1]:5173/resources/js/app.tsx:11:22)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"798 characters\">    at CurrentPage.resolveComponent (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=7660f6dd:11832:54)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"798 characters\">    at CurrentPage.resolve (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=7660f6dd:9554:33)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"798 characters\">    at CurrentPage.set (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=7660f6dd:9484:17)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"798 characters\">    at _Response.setPage (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=7660f6dd:10535:17)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"798 characters\">    at async _Response.process (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=7660f6dd:10462:5)</span>\n              \"\"\"\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n      \"<span class=sf-dump-key>userAgent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1301202064 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1181</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"958 characters\">mintlify-auth-key=ba5658bd4fdaa31823eff4f3ddd8fd98; PGADMIN_LANGUAGE=en; phpMyAdmin=6ed91cf578d851379b68c861c0577493; pma_lang=en; appearance=light; pga4_session=cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=; XSRF-TOKEN=eyJpdiI6IkY0SEhWVmtGcVhGRHFqZXplTUt0Smc9PSIsInZhbHVlIjoiTVJEeEV4WHBkd3VsbVFrZjdNN0Z1LzdRZVNFbzFqQXFsM2NNNXd4UmdFN1d0S2hhMlV5c1YzbVhsbWdIMFVtNlY2VW9tVWxHd0tLNG5wamVWMW5yNUtiZVVoNTBVSXRMQzh4aGFIZmJwTWFYRjI0S1BuMUFNUC9IeWJiUnNSbTIiLCJtYWMiOiI0NDZmMmU5ZTllOTVjODY4NTlkZTEyMjlkMzFjYjE3OTRhYzY5NGVhY2ZmZWUxMTU0YzhiYjk4MDUyOWE1MmZkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik12UGd3V21CdUxrY0c0ZnZJeWdGV2c9PSIsInZhbHVlIjoiMUR5cE5BMXE3T21tTHVlWENieWZvUHBNdk15NVk1MzQ4b1ZLUjhpMlIrUTgyN0FWQVBDd0FxTXk3Wm5oa2pwNE5MeU5XODNEc0tRdEEvbTRoVkY3ZGcxNXNEejFQUVA2d25uSHQ4dklnazNCUFRabUw3dlFFRWRTeGJIaDkrdDciLCJtYWMiOiI3ZGRkYmRhMmFmODdiNmY3YzlmMTA1YzYxNDRiOWM1OTA5YzY0MmQ5OTBkNzg2OTRiYWE3YTAwYzk4YzU0NjkwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1301202064\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1889228355 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>mintlify-auth-key</span>\" => \"<span class=sf-dump-str title=\"32 characters\">ba5658bd4fdaa31823eff4f3ddd8fd98</span>\"\n  \"<span class=sf-dump-key>PGADMIN_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>phpMyAdmin</span>\" => \"<span class=sf-dump-str title=\"32 characters\">6ed91cf578d851379b68c861c0577493</span>\"\n  \"<span class=sf-dump-key>pma_lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>pga4_session</span>\" => \"<span class=sf-dump-str title=\"81 characters\">cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkY0SEhWVmtGcVhGRHFqZXplTUt0Smc9PSIsInZhbHVlIjoiTVJEeEV4WHBkd3VsbVFrZjdNN0Z1LzdRZVNFbzFqQXFsM2NNNXd4UmdFN1d0S2hhMlV5c1YzbVhsbWdIMFVtNlY2VW9tVWxHd0tLNG5wamVWMW5yNUtiZVVoNTBVSXRMQzh4aGFIZmJwTWFYRjI0S1BuMUFNUC9IeWJiUnNSbTIiLCJtYWMiOiI0NDZmMmU5ZTllOTVjODY4NTlkZTEyMjlkMzFjYjE3OTRhYzY5NGVhY2ZmZWUxMTU0YzhiYjk4MDUyOWE1MmZkIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik12UGd3V21CdUxrY0c0ZnZJeWdGV2c9PSIsInZhbHVlIjoiMUR5cE5BMXE3T21tTHVlWENieWZvUHBNdk15NVk1MzQ4b1ZLUjhpMlIrUTgyN0FWQVBDd0FxTXk3Wm5oa2pwNE5MeU5XODNEc0tRdEEvbTRoVkY3ZGcxNXNEejFQUVA2d25uSHQ4dklnazNCUFRabUw3dlFFRWRTeGJIaDkrdDciLCJtYWMiOiI3ZGRkYmRhMmFmODdiNmY3YzlmMTA1YzYxNDRiOWM1OTA5YzY0MmQ5OTBkNzg2OTRiYWE3YTAwYzk4YzU0NjkwIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1889228355\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1231582027 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 31 Aug 2025 06:46:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1231582027\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-873981289 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-873981289\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure"}, "badge": null}}