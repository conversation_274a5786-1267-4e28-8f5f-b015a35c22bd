{"__meta": {"id": "01K3ZEK6VFS716KD4CMGRDVB9K", "datetime": "2025-08-31 06:56:19", "utime": **********.313293, "method": "GET", "uri": "/wallet/verify?trxref=wallet_DUipIry9kp_1756623358&reference=wallet_DUipIry9kp_1756623358", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.022752, "end": **********.313325, "duration": 1.2905728816986084, "duration_str": "1.29s", "measures": [{"label": "Booting", "start": **********.022752, "relative_start": 0, "end": **********.310433, "relative_end": **********.310433, "duration": 0.*****************, "duration_str": "288ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.310443, "relative_start": 0.****************, "end": **********.313327, "relative_end": 2.1457672119140625e-06, "duration": 1.****************, "duration_str": "1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.34609, "relative_start": 0.*****************, "end": **********.351781, "relative_end": **********.351781, "duration": 0.005690813064575195, "duration_str": "5.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.308426, "relative_start": 1.****************, "end": **********.309174, "relative_end": **********.309174, "duration": 0.0007481575012207031, "duration_str": "748μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 9, "nb_statements": 6, "nb_visible_statements": 9, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.08468999999999999, "accumulated_duration_str": "84.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.374635, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"sessions\" where \"id\" = 'KJKDUFy6hWS2wyZjWuEYDEtGFDEFZSo0ibJUKc7t' limit 1", "type": "query", "params": [], "bindings": ["KJKDUFy6hWS2wyZjWuEYDEtGFDEFZSo0ibJUKc7t"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.383724, "duration": 0.05773, "duration_str": "57.73ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 68.166}, {"sql": "select * from \"users\" where \"id\" = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.462758, "duration": 0.0079, "duration_str": "7.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "thesylink", "explain": null, "start_percent": 68.166, "width_percent": 9.328}, {"sql": "select * from \"wallet_transactions\" where \"payment_reference\" = 'wallet_DUipIry9kp_1756623358' limit 1", "type": "query", "params": [], "bindings": ["wallet_DUipIry9kp_1756623358"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/WalletController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\WalletController.php", "line": 118}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.133067, "duration": 0.008289999999999999, "duration_str": "8.29ms", "memory": 0, "memory_str": null, "filename": "WalletController.php:118", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/WalletController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\WalletController.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FWalletController.php&line=118", "ajax": false, "filename": "WalletController.php", "line": "118"}, "connection": "thesylink", "explain": null, "start_percent": 77.494, "width_percent": 9.789}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Controllers/WalletController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\WalletController.php", "line": 121}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.218657, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "WalletController.php:121", "source": {"index": 10, "namespace": null, "name": "app/Http/Controllers/WalletController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\WalletController.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FWalletController.php&line=121", "ajax": false, "filename": "WalletController.php", "line": "121"}, "connection": "thesylink", "explain": null, "start_percent": 87.283, "width_percent": 0}, {"sql": "select * from \"users\" where \"users\".\"id\" = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/WalletController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\WalletController.php", "line": 122}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/WalletController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\WalletController.php", "line": 121}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.2227492, "duration": 0.0029, "duration_str": "2.9ms", "memory": 0, "memory_str": null, "filename": "WalletController.php:122", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/WalletController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\WalletController.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FWalletController.php&line=122", "ajax": false, "filename": "WalletController.php", "line": "122"}, "connection": "thesylink", "explain": null, "start_percent": 87.283, "width_percent": 3.424}, {"sql": "update \"users\" set \"wallet_balance\" = 100, \"updated_at\" = '2025-08-31 06:56:19' where \"id\" = 1", "type": "query", "params": [], "bindings": [100, "2025-08-31 06:56:19", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/WalletController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\WalletController.php", "line": 127}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/WalletController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\WalletController.php", "line": 121}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.2322, "duration": 0.00438, "duration_str": "4.38ms", "memory": 0, "memory_str": null, "filename": "WalletController.php:127", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/WalletController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\WalletController.php", "line": 127}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FWalletController.php&line=127", "ajax": false, "filename": "WalletController.php", "line": "127"}, "connection": "thesylink", "explain": null, "start_percent": 90.707, "width_percent": 5.172}, {"sql": "update \"wallet_transactions\" set \"balance_after\" = 100, \"status\" = 'completed', \"metadata\" = '{\"id\":**********,\"domain\":\"test\",\"status\":\"success\",\"reference\":\"wallet_DUipIry9kp_1756623358\",\"receipt_number\":\"10101\",\"amount\":10000,\"message\":null,\"gateway_response\":\"Approved\",\"paid_at\":\"2025-08-31T06:56:10.000Z\",\"created_at\":\"2025-08-31T06:56:02.000Z\",\"channel\":\"mobile_money\",\"currency\":\"GHS\",\"ip_address\":\"*************\",\"metadata\":{\"user_id\":\"1\",\"transaction_id\":\"1\",\"referrer\":\"http:\\/\\/localhost:8000\\/\"},\"log\":{\"start_time\":**********,\"time_spent\":3,\"attempts\":1,\"errors\":0,\"success\":false,\"mobile\":false,\"input\":[],\"history\":[{\"type\":\"action\",\"message\":\"Attempted to pay with mobile money\",\"time\":3}]},\"fees\":195,\"fees_split\":null,\"authorization\":{\"authorization_code\":\"AUTH_39ge0jgi45\",\"bin\":\"055XXX\",\"last4\":\"X987\",\"exp_month\":\"12\",\"exp_year\":\"9999\",\"channel\":\"mobile_money\",\"card_type\":\"\",\"bank\":\"MTN\",\"country_code\":\"GH\",\"brand\":\"Mtn\",\"reusable\":false,\"signature\":null,\"account_name\":null,\"mobile_money_number\":\"**********\",\"receiver_bank_account_number\":null,\"receiver_bank\":null},\"customer\":{\"id\":*********,\"first_name\":null,\"last_name\":null,\"email\":\"<EMAIL>\",\"customer_code\":\"CUS_2f8f608n0j73bwn\",\"phone\":null,\"metadata\":null,\"risk_action\":\"default\",\"international_format_phone\":null},\"plan\":null,\"split\":[],\"order_id\":null,\"paidAt\":\"2025-08-31T06:56:10.000Z\",\"createdAt\":\"2025-08-31T06:56:02.000Z\",\"requested_amount\":10000,\"pos_transaction_data\":null,\"source\":null,\"fees_breakdown\":null,\"connect\":null,\"transaction_date\":\"2025-08-31T06:56:02.000Z\",\"plan_object\":[],\"subaccount\":[]}', \"updated_at\" = '2025-08-31 06:56:19' where \"id\" = 1", "type": "query", "params": [], "bindings": [100, "completed", "{\"id\":**********,\"domain\":\"test\",\"status\":\"success\",\"reference\":\"wallet_DUipIry9kp_1756623358\",\"receipt_number\":\"10101\",\"amount\":10000,\"message\":null,\"gateway_response\":\"Approved\",\"paid_at\":\"2025-08-31T06:56:10.000Z\",\"created_at\":\"2025-08-31T06:56:02.000Z\",\"channel\":\"mobile_money\",\"currency\":\"GHS\",\"ip_address\":\"*************\",\"metadata\":{\"user_id\":\"1\",\"transaction_id\":\"1\",\"referrer\":\"http:\\/\\/localhost:8000\\/\"},\"log\":{\"start_time\":**********,\"time_spent\":3,\"attempts\":1,\"errors\":0,\"success\":false,\"mobile\":false,\"input\":[],\"history\":[{\"type\":\"action\",\"message\":\"Attempted to pay with mobile money\",\"time\":3}]},\"fees\":195,\"fees_split\":null,\"authorization\":{\"authorization_code\":\"AUTH_39ge0jgi45\",\"bin\":\"055XXX\",\"last4\":\"X987\",\"exp_month\":\"12\",\"exp_year\":\"9999\",\"channel\":\"mobile_money\",\"card_type\":\"\",\"bank\":\"MTN\",\"country_code\":\"GH\",\"brand\":\"Mtn\",\"reusable\":false,\"signature\":null,\"account_name\":null,\"mobile_money_number\":\"**********\",\"receiver_bank_account_number\":null,\"receiver_bank\":null},\"customer\":{\"id\":*********,\"first_name\":null,\"last_name\":null,\"email\":\"<EMAIL>\",\"customer_code\":\"CUS_2f8f608n0j73bwn\",\"phone\":null,\"metadata\":null,\"risk_action\":\"default\",\"international_format_phone\":null},\"plan\":null,\"split\":[],\"order_id\":null,\"paidAt\":\"2025-08-31T06:56:10.000Z\",\"createdAt\":\"2025-08-31T06:56:02.000Z\",\"requested_amount\":10000,\"pos_transaction_data\":null,\"source\":null,\"fees_breakdown\":null,\"connect\":null,\"transaction_date\":\"2025-08-31T06:56:02.000Z\",\"plan_object\":[],\"subaccount\":[]}", "2025-08-31 06:56:19", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/WalletController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\WalletController.php", "line": 130}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/WalletController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\WalletController.php", "line": 121}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.241766, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "WalletController.php:130", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/WalletController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\WalletController.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FWalletController.php&line=130", "ajax": false, "filename": "WalletController.php", "line": "130"}, "connection": "thesylink", "explain": null, "start_percent": 95.879, "width_percent": 4.121}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/WalletController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\WalletController.php", "line": 121}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.300555, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "WalletController.php:121", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/WalletController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\WalletController.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FWalletController.php&line=121", "ajax": false, "filename": "WalletController.php", "line": "121"}, "connection": "thesylink", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 2, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\WalletTransaction": {"retrieved": 1, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FWalletTransaction.php&line=1", "ajax": false, "filename": "WalletTransaction.php", "line": "?"}}}, "count": 5, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 3, "updated": 2}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://localhost:8000/wallet/verify?reference=wallet_DUipIry9kp_1756623358&trxref=wallet_DUipIry9kp_...", "action_name": "wallet.verify", "controller_action": "App\\Http\\Controllers\\WalletController@verifyPayment", "uri": "GET wallet/verify", "controller": "App\\Http\\Controllers\\WalletController@verifyPayment<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FWalletController.php&line=96\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/wallet", "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FWalletController.php&line=96\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/WalletController.php:96-144</a>", "middleware": "web, auth, verified", "duration": "1.29s", "peak_memory": "26MB", "response": "Redirect to http://localhost:8000/wallet/add-funds", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1371419557 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>trxref</span>\" => \"<span class=sf-dump-str title=\"28 characters\">wallet_DUipIry9kp_1756623358</span>\"\n  \"<span class=sf-dump-key>reference</span>\" => \"<span class=sf-dump-str title=\"28 characters\">wallet_DUipIry9kp_1756623358</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1371419557\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-741725315 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-741725315\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"848 characters\">PGADMIN_LANGUAGE=en; appearance=light; pga4_session=cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=; XSRF-TOKEN=eyJpdiI6ImxHZE5QYkZUeTVtZFNGLys0V3hwOUE9PSIsInZhbHVlIjoiRURIYUVQZG9NVzdWMklTdGVTSmFvajJ1OGlMR05ERUQvWDlkK3VROHZZUTBERnJhV3RGbkdEcTBmTFQ0MWdJcUQ3M1FEZjBqc3lmSmhrdTlMbndnT1M0SkJVMWFEOGF3TWZvSXk3em1sN0R0dUpBaHRrd0VLdlJ5aHh5Tzd3YVUiLCJtYWMiOiJjZGQxY2M1YjIyODgyMWIzZjE2OGI0YTk1NDg1YmQwMDNiZGEzZDM3NjNjOGU0ZDJjODUyMmZjMTJhZDNhY2MxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InRkYWdqVXlncWsveHJ6UkY2L1VOYVE9PSIsInZhbHVlIjoia3NqMWhPcU9nZ2JNaXhzSTZlSEpmdzhtWVVodGM4VnFhTTN0TzFIT3BzanJ2UW5nSGQyMTFxVmE1ait4RkJwZFhCeUZvR0x0TlNWbC9EU0liVWJsS1hDa1lyVm8xNGZwUkVmeFZiTnhOWkFzTWhuYW1nbDRDVWJLMHFrOXFPRWkiLCJtYWMiOiI0NWM2NmY0YTk2MWFmZDdmOGRkZDc4ZTEwMTIyYTZlMzlmZDFlYzdmNTQ3MGI5OTNmYjU4NDMzYjZlOGU0N2ExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2116394821 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>PGADMIN_LANGUAGE</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>pga4_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fjc6Xqj4DjCh1KJJxnc8QSb5AvgZxSU9mzlDDgnp</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KJKDUFy6hWS2wyZjWuEYDEtGFDEFZSo0ibJUKc7t</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2116394821\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1542559951 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 31 Aug 2025 06:56:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://localhost:8000/wallet/add-funds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1542559951\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1669500335 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fjc6Xqj4DjCh1KJJxnc8QSb5AvgZxSU9mzlDDgnp</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://localhost:8000/wallet/add-funds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Payment successful! Your wallet has been credited.</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1669500335\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://localhost:8000/wallet/verify?reference=wallet_DUipIry9kp_1756623358&trxref=wallet_DUipIry9kp_...", "action_name": "wallet.verify", "controller_action": "App\\Http\\Controllers\\WalletController@verifyPayment"}, "badge": "302 Found"}}