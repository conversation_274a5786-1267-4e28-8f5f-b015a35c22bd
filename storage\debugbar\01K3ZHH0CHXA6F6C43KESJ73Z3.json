{"__meta": {"id": "01K3ZHH0CHXA6F6C43KESJ73Z3", "datetime": "2025-08-31 07:47:32", "utime": **********.882748, "method": "POST", "uri": "/_boost/browser-logs", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[07:47:32] LOG.warning: Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}. {\n    \"url\": \"http:\\/\\/localhost:8000\\/profile\",\n    \"user_agent\": \"Mozilla\\/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit\\/605.1.15 (KHTML, like Gecko) Version\\/16.6 Mobile\\/15E148 Safari\\/604.1\",\n    \"timestamp\": \"2025-08-31T07:47:31.972Z\"\n}", "message_html": null, "is_string": false, "label": "warning", "time": **********.83567, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 5, "start": **********.406398, "end": **********.882781, "duration": 0.4763829708099365, "duration_str": "476ms", "measures": [{"label": "Booting", "start": **********.406398, "relative_start": 0, "end": **********.772126, "relative_end": **********.772126, "duration": 0.*****************, "duration_str": "366ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.772156, "relative_start": 0.*****************, "end": **********.882785, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "111ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.82026, "relative_start": 0.****************, "end": **********.827106, "relative_end": **********.827106, "duration": 0.006845951080322266, "duration_str": "6.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.876285, "relative_start": 0.*****************, "end": **********.877481, "relative_end": **********.877481, "duration": 0.0011959075927734375, "duration_str": "1.2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.877526, "relative_start": 0.***************, "end": **********.877594, "relative_end": **********.877594, "duration": 6.794929504394531e-05, "duration_str": "68μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "21MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 0, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionServiceProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php", "line": 52}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1153}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 971}], "start": **********.867408, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure", "uri": "POST _boost/browser-logs", "excluded_middleware": ["Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken"], "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fboost%2Fsrc%2FBoostServiceProvider.php&line=100\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/laravel/boost/src/BoostServiceProvider.php:100-126</a>", "duration": "478ms", "peak_memory": "22MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-94495472 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-94495472\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>logs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">warn</span>\"\n      \"<span class=sf-dump-key>timestamp</span>\" => \"<span class=sf-dump-str title=\"24 characters\">2025-08-31T07:47:31.972Z</span>\"\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"85 characters\">Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}.</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost:8000/profile</span>\"\n      \"<span class=sf-dump-key>userAgent</span>\" => \"<span class=sf-dump-str title=\"135 characters\">Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1376509528 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">350</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://localhost:8000/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"958 characters\">mintlify-auth-key=ba5658bd4fdaa31823eff4f3ddd8fd98; PGADMIN_LANGUAGE=en; phpMyAdmin=6ed91cf578d851379b68c861c0577493; pma_lang=en; appearance=light; pga4_session=cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=; XSRF-TOKEN=eyJpdiI6IkVLTVJGbEgxZXpsZWZxbkg5ZHV5U0E9PSIsInZhbHVlIjoiSEZwNnlzc01OekpndTFxeW5mT2NuTktxNHQ5cVVvdVkwanF3eE1VY2VqVG9tQ25JK0VzOEViRXB3N2pIVnB2TWtvVjNoWGU4V1dnSHZMdjdQbUJhdDlzeE15eTNUaFRoK2p6RHVDTWc1VnRnVTF6NS82NHJUMWRLaUY3czFUb2MiLCJtYWMiOiI4OTRmNTAyZjMxOWUxMTBlNmZlNDY0Yzk5YjBiMDcxYWJjMjA0MDA5YjdkYWQ3YjU3MGJhZWE1NTI3YmFkNzgzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlhZMVZ4TllYbm01SUZEMTFwNTN0a3c9PSIsInZhbHVlIjoibElpTFkyYy9rZmdjT1VDRWFNcWYrd1FNVHlmbUI4WE9vczd2clczdktza3JLTEcyaXByUUhiQ3VraEJMSlNuSHZvSFB1UGUzZHVkTHExRUpVdlBSYXQ3RWdoWlRYTHJnZWh6b05vK1Z4aUJWV21QM2tzdUh3RUk2RHQ0SnEzYnQiLCJtYWMiOiIwZTAxNzQ5MTEwMjdiM2Q4ZTI4ZjgzNGVjZmRmYTBlNmUxM2JmNzZjMmNjNjNkN2UzMmNhNDE3MzM2MTBiMGE0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1376509528\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1073739393 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>mintlify-auth-key</span>\" => \"<span class=sf-dump-str title=\"32 characters\">ba5658bd4fdaa31823eff4f3ddd8fd98</span>\"\n  \"<span class=sf-dump-key>PGADMIN_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>phpMyAdmin</span>\" => \"<span class=sf-dump-str title=\"32 characters\">6ed91cf578d851379b68c861c0577493</span>\"\n  \"<span class=sf-dump-key>pma_lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>pga4_session</span>\" => \"<span class=sf-dump-str title=\"81 characters\">cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkVLTVJGbEgxZXpsZWZxbkg5ZHV5U0E9PSIsInZhbHVlIjoiSEZwNnlzc01OekpndTFxeW5mT2NuTktxNHQ5cVVvdVkwanF3eE1VY2VqVG9tQ25JK0VzOEViRXB3N2pIVnB2TWtvVjNoWGU4V1dnSHZMdjdQbUJhdDlzeE15eTNUaFRoK2p6RHVDTWc1VnRnVTF6NS82NHJUMWRLaUY3czFUb2MiLCJtYWMiOiI4OTRmNTAyZjMxOWUxMTBlNmZlNDY0Yzk5YjBiMDcxYWJjMjA0MDA5YjdkYWQ3YjU3MGJhZWE1NTI3YmFkNzgzIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlhZMVZ4TllYbm01SUZEMTFwNTN0a3c9PSIsInZhbHVlIjoibElpTFkyYy9rZmdjT1VDRWFNcWYrd1FNVHlmbUI4WE9vczd2clczdktza3JLTEcyaXByUUhiQ3VraEJMSlNuSHZvSFB1UGUzZHVkTHExRUpVdlBSYXQ3RWdoWlRYTHJnZWh6b05vK1Z4aUJWV21QM2tzdUh3RUk2RHQ0SnEzYnQiLCJtYWMiOiIwZTAxNzQ5MTEwMjdiM2Q4ZTI4ZjgzNGVjZmRmYTBlNmUxM2JmNzZjMmNjNjNkN2UzMmNhNDE3MzM2MTBiMGE0IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1073739393\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2095071514 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 31 Aug 2025 07:47:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2095071514\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1671404090 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1671404090\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure"}, "badge": null}}